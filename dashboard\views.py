from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login
from django.contrib.auth.forms import AuthenticationForm
from django.contrib.auth.decorators import login_required
from django.contrib.auth import get_user
from django.db.models import F
from definitions.models import (
    PersonDefinition, ProductDefinition, WarehouseDefinition,
    CurrencyDefinition, BankDefinition
)

def login_view(request):
    if request.method == 'POST':
        form = AuthenticationForm(request, data=request.POST)
        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            user = authenticate(request, username=username, password=password)
            if user is not None:
                login(request, user)
                return redirect('dashboard_home')
    else:
        form = AuthenticationForm()
    return render(request, 'registration/login.html', {'form': form})

@login_required
def dashboard_home(request):
    # إحصائيات من تطبيق التعريفات
    customers_count = PersonDefinition.objects.filter(person_type__in=['customer', 'both']).count()
    suppliers_count = PersonDefinition.objects.filter(person_type__in=['supplier', 'both']).count()
    products_count = ProductDefinition.objects.count()
    warehouses_count = WarehouseDefinition.objects.count()
    currencies_count = CurrencyDefinition.objects.count()
    banks_count = BankDefinition.objects.count()

    # منتجات تحت الحد الأدنى
    low_stock_count = ProductDefinition.objects.filter(
        minimum_stock__gt=0,
        is_active=True,
        track_inventory=True
    ).count()

    user = get_user(request)
    return render(request, 'dashboard/home.html', {
        'customers_count': customers_count,
        'suppliers_count': suppliers_count,
        'products_count': products_count,
        'warehouses_count': warehouses_count,
        'currencies_count': currencies_count,
        'banks_count': banks_count,
        'low_stock_count': low_stock_count,
        'user': user,
    })
