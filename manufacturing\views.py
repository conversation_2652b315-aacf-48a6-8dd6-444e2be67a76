from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse

@login_required
def manufacturing_dashboard(request):
    """لوحة تحكم التصنيع"""
    context = {
        'message': 'مرحباً بك في قسم التصنيع - قيد التطوير'
    }
    return render(request, 'manufacturing/dashboard.html', context)

@login_required
def production_order_list(request):
    """قائمة أوامر الإنتاج"""
    messages.info(request, 'هذه الصفحة قيد التطوير')
    return render(request, 'manufacturing/dashboard.html', {})

@login_required
def production_order_create(request):
    """إنشاء أمر إنتاج"""
    messages.info(request, 'هذه الصفحة قيد التطوير')
    return render(request, 'manufacturing/dashboard.html', {})

@login_required
def production_order_detail(request, pk):
    """تفاصيل أمر الإنتاج"""
    messages.info(request, 'هذه الصفحة قيد التطوير')
    return render(request, 'manufacturing/dashboard.html', {})

@login_required
def get_material_info(request):
    """API للحصول على معلومات المواد"""
    return JsonResponse({'message': 'قيد التطوير'})

@login_required
def calculate_production_cost(request):
    """API لحساب تكلفة الإنتاج"""
    return JsonResponse({'message': 'قيد التطوير'})
