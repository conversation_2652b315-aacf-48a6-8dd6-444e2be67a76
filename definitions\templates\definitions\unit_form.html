{% extends 'base.html' %}

{% block title %}{{ title }} - أوساريك{% endblock %}

{% block content %}
    <div class="page-header">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard_home' %}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{% url 'definitions:dashboard' %}">التعريفات</a></li>
                <li class="breadcrumb-item"><a href="{% url 'definitions:unit_list' %}">الوحدات</a></li>
                <li class="breadcrumb-item active">{{ title }}</li>
            </ol>
        </nav>
        <h1 class="page-title">{{ title }}</h1>
        <p class="page-subtitle">
            {% if unit %}
                تعديل بيانات الوحدة في النظام
            {% else %}
                إضافة وحدة جديدة إلى النظام
            {% endif %}
        </p>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-rulers me-2"></i>بيانات الوحدة
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">
                                    <i class="bi bi-tag me-1"></i>{{ form.name.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.symbol.id_for_label }}" class="form-label">
                                    <i class="bi bi-code me-1"></i>{{ form.symbol.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.symbol }}
                                {% if form.symbol.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.symbol.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">مثال: كجم، متر، لتر</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.unit_type.id_for_label }}" class="form-label">
                                    <i class="bi bi-list me-1"></i>{{ form.unit_type.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.unit_type }}
                                {% if form.unit_type.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.unit_type.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.base_unit.id_for_label }}" class="form-label">
                                    <i class="bi bi-arrow-up me-1"></i>{{ form.base_unit.label }}
                                </label>
                                {{ form.base_unit }}
                                {% if form.base_unit.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.base_unit.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">اتركه فارغاً إذا كانت وحدة أساسية</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.conversion_factor.id_for_label }}" class="form-label">
                                    <i class="bi bi-arrow-left-right me-1"></i>{{ form.conversion_factor.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.conversion_factor }}
                                {% if form.conversion_factor.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.conversion_factor.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">معامل التحويل إلى الوحدة الأساسية</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الإعدادات</label>
                                <div class="form-check">
                                    {{ form.is_active }}
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        {{ form.is_active.label }}
                                    </label>
                                    {% if form.is_active.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.is_active.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'definitions:unit_list' %}" class="btn btn-secondary">
                                <i class="bi bi-arrow-right me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-lg me-2"></i>{{ button_text }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            {% if unit %}
                <!-- Unit Information -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-info-circle me-2"></i>معلومات الوحدة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="border rounded p-3">
                                    <i class="bi bi-rulers text-primary" style="font-size: 2rem;"></i>
                                    <h4 class="mt-2 mb-1">{{ unit.symbol }}</h4>
                                    <p class="text-muted mb-0">رمز الوحدة</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border rounded p-3">
                                    <i class="bi bi-list text-success" style="font-size: 2rem;"></i>
                                    <h4 class="mt-2 mb-1">{{ unit.get_unit_type_display }}</h4>
                                    <p class="text-muted mb-0">نوع الوحدة</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border rounded p-3">
                                    <i class="bi bi-arrow-left-right text-info" style="font-size: 2rem;"></i>
                                    <h4 class="mt-2 mb-1">{{ unit.conversion_factor }}</h4>
                                    <p class="text-muted mb-0">معامل التحويل</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border rounded p-3">
                                    <i class="bi bi-calendar-event text-warning" style="font-size: 2rem;"></i>
                                    <h4 class="mt-2 mb-1">{{ unit.created_at|date:"Y/m/d" }}</h4>
                                    <p class="text-muted mb-0">تاريخ الإضافة</p>
                                </div>
                            </div>
                        </div>
                        
                        {% if unit.base_unit %}
                            <div class="alert alert-info mt-3">
                                <i class="bi bi-info-circle me-2"></i>
                                <strong>الوحدة الأساسية:</strong> {{ unit.base_unit.name }} ({{ unit.base_unit.symbol }})
                            </div>
                        {% else %}
                            <div class="alert alert-success mt-3">
                                <i class="bi bi-star me-2"></i>
                                <strong>هذه وحدة أساسية</strong> - لا تحتاج إلى تحويل
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Conversion Examples -->
                {% if unit.base_unit %}
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-calculator me-2"></i>أمثلة التحويل
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="bg-light p-3 rounded">
                                        <h6>من {{ unit.name }} إلى {{ unit.base_unit.name }}</h6>
                                        <p class="mb-0">1 {{ unit.symbol }} = {{ unit.conversion_factor }} {{ unit.base_unit.symbol }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="bg-light p-3 rounded">
                                        <h6>من {{ unit.base_unit.name }} إلى {{ unit.name }}</h6>
                                        <p class="mb-0">1 {{ unit.base_unit.symbol }} = {{ unit.conversion_factor|floatformat:4|div:1 }} {{ unit.symbol }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endif %}
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block extra_css %}
<style>
    .form-label {
        font-weight: 600;
        color: #495057;
    }
    
    .form-control:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }
    
    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }
    
    .breadcrumb {
        background-color: transparent;
        padding: 0;
        margin-bottom: 1rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        content: "←";
    }
    
    .form-check {
        margin-bottom: 0.5rem;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Form validation and enhancements
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.querySelector('form');
        const unitTypeSelect = document.getElementById('{{ form.unit_type.id_for_label }}');
        const baseUnitSelect = document.getElementById('{{ form.base_unit.id_for_label }}');
        const conversionFactorInput = document.getElementById('{{ form.conversion_factor.id_for_label }}');
        
        // Filter base units based on unit type
        unitTypeSelect.addEventListener('change', function() {
            // This would require AJAX to filter base units by type
            // For now, we'll just show a message
            if (this.value) {
                console.log('Selected unit type:', this.value);
            }
        });
        
        // Base unit change handler
        baseUnitSelect.addEventListener('change', function() {
            if (this.value === '') {
                conversionFactorInput.value = '1.0000';
                conversionFactorInput.disabled = true;
            } else {
                conversionFactorInput.disabled = false;
                if (conversionFactorInput.value === '1.0000') {
                    conversionFactorInput.value = '';
                }
            }
        });
        
        // Initialize conversion factor state
        if (baseUnitSelect.value === '') {
            conversionFactorInput.value = '1.0000';
            conversionFactorInput.disabled = true;
        }
        
        // Form submission
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري الحفظ...';
            submitBtn.disabled = true;
        });
    });
</script>
{% endblock %}
