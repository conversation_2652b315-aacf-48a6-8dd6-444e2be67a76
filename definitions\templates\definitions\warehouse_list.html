{% extends 'base.html' %}
{% load static %}

{% block title %}تعريف المخازن{% endblock %}

{% block extra_css %}
<style>
    .warehouse-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem 0;
        margin-bottom: 2rem;
        color: white;
        border-radius: 15px;
    }

    .search-filters {
        background: white;
        padding: 1.5rem;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }

    .warehouse-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        overflow: hidden;
        margin-bottom: 1.5rem;
    }

    .warehouse-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .warehouse-card-header {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        padding: 1rem 1.5rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .warehouse-type-badge {
        background: rgba(255,255,255,0.2);
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .warehouse-card-body {
        padding: 1.5rem;
    }

    .warehouse-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .info-item {
        display: flex;
        align-items: center;
        color: #666;
    }

    .info-item i {
        margin-left: 0.5rem;
        color: #667eea;
        width: 20px;
    }

    .warehouse-actions {
        display: flex;
        gap: 0.5rem;
        justify-content: flex-end;
    }

    .btn-action {
        padding: 0.5rem 1rem;
        border-radius: 8px;
        border: none;
        font-size: 0.85rem;
        font-weight: 500;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
    }

    .btn-view {
        background: #e3f2fd;
        color: #1976d2;
    }

    .btn-edit {
        background: #fff3e0;
        color: #f57c00;
    }

    .btn-delete {
        background: #ffebee;
        color: #d32f2f;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .pagination-wrapper {
        display: flex;
        justify-content: center;
        margin-top: 2rem;
    }

    .stats-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        padding: 1.5rem;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        text-align: center;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #667eea;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #666;
        font-size: 0.9rem;
    }

    /* Table Styles */
    .warehouse-table {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .table {
        margin-bottom: 0;
    }

    .table thead th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 1rem;
        font-weight: 600;
        text-align: center;
        vertical-align: middle;
        font-size: 0.9rem;
    }

    .table tbody td {
        padding: 1rem 0.75rem;
        vertical-align: middle;
        border-bottom: 1px solid #eee;
        font-size: 0.875rem;
    }

    .table tbody tr:hover {
        background-color: #f8f9fa;
    }

    .table tbody tr:last-child td {
        border-bottom: none;
    }

    .warehouse-code {
        font-weight: 600;
        color: #495057;
        font-family: 'Courier New', monospace;
        background: #f8f9fa;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.8rem;
    }

    .warehouse-name {
        font-weight: 600;
        color: #212529;
        margin-bottom: 0.25rem;
    }

    .warehouse-type-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 500;
        background: #e3f2fd;
        color: #1565c0;
        display: inline-block;
    }

    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 500;
        display: inline-block;
    }

    .status-active {
        background: #d4edda;
        color: #155724;
    }

    .status-inactive {
        background: #f8d7da;
        color: #721c24;
    }

    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        color: #dee2e6;
    }
</style>
{% endblock %}

{% block content %}
<!-- Warehouse Header -->
<div class="warehouse-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-building me-2"></i>
                    تعريف المخازن
                </h1>
                <p class="mb-0 opacity-75">عرض وإدارة جميع المخازن والمستودعات</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'definitions:dashboard' %}" class="btn btn-outline-light me-2">
                    <i class="bi bi-arrow-left me-2"></i>
                    عودة للتعريفات
                </a>
                <a href="{% url 'definitions:warehouse_create' %}" class="btn btn-light btn-lg">
                    <i class="bi bi-plus-circle me-2"></i>
                    إضافة مخزن جديد
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics -->
<div class="container-fluid">
    <div class="stats-row">
        <div class="stat-card">
            <div class="stat-number">{{ total_warehouses }}</div>
            <div class="stat-label">إجمالي المخازن</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ page_obj.paginator.count }}</div>
            <div class="stat-label">المخازن المعروضة</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ page_obj.paginator.num_pages }}</div>
            <div class="stat-label">عدد الصفحات</div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="search-filters">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">البحث</label>
                <input type="text" class="form-control" name="search" value="{{ search_query }}" 
                       placeholder="البحث بالاسم أو الكود أو المدير...">
            </div>
            <div class="col-md-3">
                <label class="form-label">نوع المخزن</label>
                <select class="form-select" name="type">
                    <option value="">جميع الأنواع</option>
                    {% for value, label in warehouse_types %}
                        <option value="{{ value }}" {% if warehouse_type == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="bi bi-search me-1"></i>بحث
                </button>
                <a href="{% url 'definitions:warehouse_list' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-clockwise me-1"></i>إعادة تعيين
                </a>
            </div>
        </form>
    </div>

    <!-- Warehouses Table -->
    <div class="warehouse-table">
        {% if page_obj %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th style="width: 10%;">الكود</th>
                        <th style="width: 20%;">اسم المخزن</th>
                        <th style="width: 15%;">النوع</th>
                        <th style="width: 15%;">المدير</th>
                        <th style="width: 10%;">الهاتف</th>
                        <th style="width: 10%;">الحالة</th>
                        <th style="width: 10%;">تاريخ الإنشاء</th>
                        <th style="width: 10%;">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for warehouse in page_obj %}
                    <tr>
                        <td>
                            <span class="warehouse-code">{{ warehouse.code }}</span>
                        </td>
                        <td>
                            <div class="warehouse-name">{{ warehouse.name }}</div>
                            {% if warehouse.address %}
                            <small class="text-muted">
                                <i class="bi bi-geo-alt me-1"></i>{{ warehouse.address|truncatechars:25 }}
                            </small>
                            {% endif %}
                        </td>
                        <td>
                            <span class="warehouse-type-badge">
                                {{ warehouse.get_warehouse_type_display }}
                            </span>
                        </td>
                        <td>
                            {% if warehouse.manager_name %}
                                <i class="bi bi-person me-1"></i>{{ warehouse.manager_name }}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if warehouse.phone %}
                                <a href="tel:{{ warehouse.phone }}" class="text-decoration-none">
                                    <i class="bi bi-telephone me-1"></i>{{ warehouse.phone }}
                                </a>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="status-badge {% if warehouse.is_active %}status-active{% else %}status-inactive{% endif %}">
                                <i class="bi bi-{% if warehouse.is_active %}check-circle{% else %}x-circle{% endif %} me-1"></i>
                                {{ warehouse.is_active|yesno:"نشط,غير نشط" }}
                            </span>
                        </td>
                        <td>
                            <small>{{ warehouse.created_at|date:"d/m/Y" }}</small>
                        </td>
                        <td>
                            <div class="warehouse-actions">
                                <a href="{% url 'definitions:warehouse_detail' warehouse.id %}"
                                   class="btn-action btn-view"
                                   title="عرض التفاصيل">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{% url 'definitions:warehouse_edit' warehouse.id %}"
                                   class="btn-action btn-edit"
                                   title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <form method="post" action="{% url 'definitions:warehouse_delete' warehouse.id %}" style="display: inline;" class="delete-form">
                                    {% csrf_token %}
                                    <button type="button"
                                            class="btn-action btn-delete delete-warehouse-btn"
                                            title="حذف"
                                            data-warehouse-name="{{ warehouse.name }}"
                                            data-warehouse-id="{{ warehouse.id }}">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="empty-state">
            <i class="bi bi-building"></i>
            <h4 class="mt-3">لا توجد مخازن</h4>
            <p class="mb-3">لم يتم العثور على أي مخازن تطابق معايير البحث</p>
            <a href="{% url 'definitions:warehouse_create' %}" class="btn btn-primary">
                <i class="bi bi-plus-circle me-2"></i>إضافة مخزن جديد
            </a>
        </div>
        {% endif %}
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="pagination-wrapper">
        <nav aria-label="تنقل الصفحات">
            <ul class="pagination">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if warehouse_type %}&type={{ warehouse_type }}{% endif %}">الأولى</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if warehouse_type %}&type={{ warehouse_type }}{% endif %}">السابقة</a>
                    </li>
                {% endif %}

                <li class="page-item active">
                    <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
                </li>

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if warehouse_type %}&type={{ warehouse_type }}{% endif %}">التالية</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if warehouse_type %}&type={{ warehouse_type }}{% endif %}">الأخيرة</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
    </div>
    {% endif %}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // معالجة أزرار الحذف
    const deleteButtons = document.querySelectorAll('.delete-warehouse-btn');

    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const warehouseName = this.getAttribute('data-warehouse-name');
            const warehouseId = this.getAttribute('data-warehouse-id');
            const form = this.closest('form');

            console.log('Delete button clicked for:', warehouseName);

            if (confirm(`هل أنت متأكد من حذف المخزن: ${warehouseName}؟\n\nهذا الإجراء لا يمكن التراجع عنه!`)) {
                console.log('User confirmed deletion');

                // تغيير نص الزر
                this.innerHTML = '<span class="spinner-border spinner-border-sm"></span>';
                this.disabled = true;

                console.log('Submitting form...');

                // إرسال النموذج
                form.submit();
            } else {
                console.log('User cancelled deletion');
            }
        });
    });
});
</script>
{% endblock %}
