{% extends 'base.html' %}
{% load static %}

{% block title %}
{% if action == 'edit' %}
تعديل الطابعة - {{ printer.name }}
{% else %}
إضافة طابعة جديدة
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
        min-height: 100vh;
    }

    .form-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 25px;
        padding: 3rem;
        margin-bottom: 2rem;
        box-shadow: 0 15px 50px rgba(31, 38, 135, 0.37);
        border: 1px solid rgba(255, 255, 255, 0.18);
        color: #333;
    }

    .form-title {
        font-size: 2.5rem;
        font-weight: 800;
        background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 3rem;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
    }

    .form-section {
        background: rgba(52, 73, 94, 0.1);
        backdrop-filter: blur(5px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid rgba(52, 73, 94, 0.2);
    }

    .section-title {
        font-size: 1.4rem;
        font-weight: 700;
        color: #34495e;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .form-label {
        color: #333;
        font-weight: 600;
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.95rem;
    }

    .required::after {
        content: " *";
        color: #dc3545;
        font-weight: bold;
    }

    .form-control, .form-select {
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid rgba(52, 73, 94, 0.3);
        border-radius: 15px;
        padding: 1rem 1.25rem;
        color: #333;
        transition: all 0.3s ease;
        font-size: 0.95rem;
    }

    .form-control:focus, .form-select:focus {
        background: rgba(255, 255, 255, 1);
        border-color: #34495e;
        box-shadow: 0 0 0 4px rgba(52, 73, 94, 0.2);
        color: #333;
        outline: none;
        transform: translateY(-2px);
    }

    .btn {
        padding: 1rem 2.5rem;
        border-radius: 25px;
        font-weight: 700;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        font-size: 1rem;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .btn-primary {
        background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(52, 73, 94, 0.4);
        color: white;
    }

    .btn-outline-secondary {
        background: transparent;
        border: 2px solid #6c757d;
        color: #6c757d;
    }

    .btn-outline-secondary:hover {
        background: #6c757d;
        color: white;
        text-decoration: none;
        transform: translateY(-3px);
    }

    .form-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 3rem;
        padding-top: 2rem;
        border-top: 2px solid rgba(52, 73, 94, 0.2);
    }

    .form-check {
        background: rgba(255, 255, 255, 0.7);
        border: 2px solid rgba(52, 73, 94, 0.3);
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .form-check:hover {
        background: rgba(255, 255, 255, 0.9);
        transform: translateY(-2px);
    }

    .form-check-input {
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid #34495e;
        margin-right: 0.75rem;
        width: 1.5rem;
        height: 1.5rem;
    }

    .form-check-input:checked {
        background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
        border-color: #34495e;
    }

    .form-check-label {
        color: #333;
        font-weight: 600;
        font-size: 1rem;
    }

    .form-text {
        color: #6c757d;
        font-size: 0.85rem;
        margin-top: 0.5rem;
        font-style: italic;
    }

    .alert {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.18);
        margin-bottom: 2rem;
    }

    .radio-group {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .radio-option {
        background: rgba(255, 255, 255, 0.7);
        border: 2px solid rgba(52, 73, 94, 0.3);
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .radio-option:hover {
        background: rgba(255, 255, 255, 0.9);
        transform: translateY(-2px);
    }

    .radio-option.selected {
        background: rgba(52, 73, 94, 0.2);
        border-color: #34495e;
    }

    .radio-option input[type="radio"] {
        display: none;
    }

    .radio-option-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
        color: #34495e;
    }

    .radio-option-title {
        font-weight: 700;
        color: #333;
        margin-bottom: 0.25rem;
    }

    .radio-option-desc {
        font-size: 0.85rem;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="form-container">
                <h1 class="form-title">
                    <i class="bi bi-printer"></i>
                    {% if action == 'edit' %}
                    تعديل الطابعة
                    {% else %}
                    إضافة طابعة جديدة
                    {% endif %}
                </h1>

                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            <i class="bi bi-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% else %}info-circle{% endif %} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <form method="post" id="printerForm">
                    {% csrf_token %}
                    
                    <!-- Basic Information Section -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="bi bi-info-circle"></i>
                            المعلومات الأساسية
                        </h3>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label required">
                                    <i class="bi bi-printer"></i>اسم الطابعة
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="name" 
                                       name="name" 
                                       value="{% if action == 'edit' %}{{ printer.name }}{% else %}{{ form_data.name|default:'' }}{% endif %}"
                                       placeholder="اسم الطابعة"
                                       required>
                                <div class="form-text">اسم الطابعة كما يظهر في النظام</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="model" class="form-label">
                                    <i class="bi bi-cpu"></i>الموديل
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="model" 
                                       name="model" 
                                       value="{% if action == 'edit' %}{{ printer.model }}{% else %}{{ form_data.model|default:'' }}{% endif %}"
                                       placeholder="موديل الطابعة">
                                <div class="form-text">موديل الطابعة (اختياري)</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="description" class="form-label">
                                    <i class="bi bi-file-text"></i>وصف الطابعة
                                </label>
                                <textarea class="form-control" 
                                          id="description" 
                                          name="description" 
                                          rows="3"
                                          placeholder="وصف تفصيلي للطابعة...">{% if action == 'edit' %}{{ printer.description }}{% else %}{{ form_data.description|default:'' }}{% endif %}</textarea>
                                <div class="form-text">وصف تفصيلي للطابعة (اختياري)</div>
                            </div>
                        </div>
                    </div>

                    <!-- Technical Specifications Section -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="bi bi-gear"></i>
                            المواصفات التقنية
                        </h3>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label required">
                                    <i class="bi bi-printer"></i>نوع الطابعة
                                </label>
                                <div class="radio-group">
                                    <label class="radio-option {% if action == 'edit' and printer.printer_type == 'thermal' or action != 'edit' and form_data.printer_type == 'thermal' or action != 'edit' and not form_data.printer_type %}selected{% endif %}" onclick="selectRadio(this, 'printer_type', 'thermal')">
                                        <input type="radio" name="printer_type" value="thermal" {% if action == 'edit' and printer.printer_type == 'thermal' or action != 'edit' and form_data.printer_type == 'thermal' or action != 'edit' and not form_data.printer_type %}checked{% endif %}>
                                        <div class="radio-option-icon">
                                            <i class="bi bi-thermometer"></i>
                                        </div>
                                        <div class="radio-option-title">حرارية</div>
                                        <div class="radio-option-desc">طابعة حرارية للإيصالات</div>
                                    </label>
                                    
                                    <label class="radio-option {% if action == 'edit' and printer.printer_type == 'inkjet' or action != 'edit' and form_data.printer_type == 'inkjet' %}selected{% endif %}" onclick="selectRadio(this, 'printer_type', 'inkjet')">
                                        <input type="radio" name="printer_type" value="inkjet" {% if action == 'edit' and printer.printer_type == 'inkjet' or action != 'edit' and form_data.printer_type == 'inkjet' %}checked{% endif %}>
                                        <div class="radio-option-icon">
                                            <i class="bi bi-droplet"></i>
                                        </div>
                                        <div class="radio-option-title">نفث حبر</div>
                                        <div class="radio-option-desc">طابعة نفث حبر</div>
                                    </label>
                                    
                                    <label class="radio-option {% if action == 'edit' and printer.printer_type == 'laser' or action != 'edit' and form_data.printer_type == 'laser' %}selected{% endif %}" onclick="selectRadio(this, 'printer_type', 'laser')">
                                        <input type="radio" name="printer_type" value="laser" {% if action == 'edit' and printer.printer_type == 'laser' or action != 'edit' and form_data.printer_type == 'laser' %}checked{% endif %}>
                                        <div class="radio-option-icon">
                                            <i class="bi bi-lightning"></i>
                                        </div>
                                        <div class="radio-option-title">ليزر</div>
                                        <div class="radio-option-desc">طابعة ليزر</div>
                                    </label>
                                    
                                    <label class="radio-option {% if action == 'edit' and printer.printer_type == 'dot_matrix' or action != 'edit' and form_data.printer_type == 'dot_matrix' %}selected{% endif %}" onclick="selectRadio(this, 'printer_type', 'dot_matrix')">
                                        <input type="radio" name="printer_type" value="dot_matrix" {% if action == 'edit' and printer.printer_type == 'dot_matrix' or action != 'edit' and form_data.printer_type == 'dot_matrix' %}checked{% endif %}>
                                        <div class="radio-option-icon">
                                            <i class="bi bi-grid-3x3"></i>
                                        </div>
                                        <div class="radio-option-title">نقطية</div>
                                        <div class="radio-option-desc">طابعة نقطية</div>
                                    </label>
                                </div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label required">
                                    <i class="bi bi-wifi"></i>نوع الاتصال
                                </label>
                                <div class="radio-group">
                                    <label class="radio-option {% if action == 'edit' and printer.connection_type == 'usb' or action != 'edit' and form_data.connection_type == 'usb' or action != 'edit' and not form_data.connection_type %}selected{% endif %}" onclick="selectRadio(this, 'connection_type', 'usb')">
                                        <input type="radio" name="connection_type" value="usb" {% if action == 'edit' and printer.connection_type == 'usb' or action != 'edit' and form_data.connection_type == 'usb' or action != 'edit' and not form_data.connection_type %}checked{% endif %}>
                                        <div class="radio-option-icon">
                                            <i class="bi bi-usb"></i>
                                        </div>
                                        <div class="radio-option-title">USB</div>
                                        <div class="radio-option-desc">اتصال USB</div>
                                    </label>
                                    
                                    <label class="radio-option {% if action == 'edit' and printer.connection_type == 'network' or action != 'edit' and form_data.connection_type == 'network' %}selected{% endif %}" onclick="selectRadio(this, 'connection_type', 'network')">
                                        <input type="radio" name="connection_type" value="network" {% if action == 'edit' and printer.connection_type == 'network' or action != 'edit' and form_data.connection_type == 'network' %}checked{% endif %}>
                                        <div class="radio-option-icon">
                                            <i class="bi bi-wifi"></i>
                                        </div>
                                        <div class="radio-option-title">شبكة</div>
                                        <div class="radio-option-desc">اتصال شبكة</div>
                                    </label>
                                    
                                    <label class="radio-option {% if action == 'edit' and printer.connection_type == 'bluetooth' or action != 'edit' and form_data.connection_type == 'bluetooth' %}selected{% endif %}" onclick="selectRadio(this, 'connection_type', 'bluetooth')">
                                        <input type="radio" name="connection_type" value="bluetooth" {% if action == 'edit' and printer.connection_type == 'bluetooth' or action != 'edit' and form_data.connection_type == 'bluetooth' %}checked{% endif %}>
                                        <div class="radio-option-icon">
                                            <i class="bi bi-bluetooth"></i>
                                        </div>
                                        <div class="radio-option-title">بلوتوث</div>
                                        <div class="radio-option-desc">اتصال بلوتوث</div>
                                    </label>
                                    
                                    <label class="radio-option {% if action == 'edit' and printer.connection_type == 'local' or action != 'edit' and form_data.connection_type == 'local' %}selected{% endif %}" onclick="selectRadio(this, 'connection_type', 'local')">
                                        <input type="radio" name="connection_type" value="local" {% if action == 'edit' and printer.connection_type == 'local' or action != 'edit' and form_data.connection_type == 'local' %}checked{% endif %}>
                                        <div class="radio-option-icon">
                                            <i class="bi bi-hdd"></i>
                                        </div>
                                        <div class="radio-option-title">محلي</div>
                                        <div class="radio-option-desc">اتصال محلي</div>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="ip_address" class="form-label">
                                    <i class="bi bi-router"></i>عنوان IP
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="ip_address" 
                                       name="ip_address" 
                                       value="{% if action == 'edit' %}{{ printer.ip_address }}{% else %}{{ form_data.ip_address|default:'' }}{% endif %}"
                                       placeholder="*************">
                                <div class="form-text">عنوان IP للطابعة (للطابعات الشبكية)</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="port" class="form-label">
                                    <i class="bi bi-outlet"></i>المنفذ
                                </label>
                                <input type="number" 
                                       class="form-control" 
                                       id="port" 
                                       name="port" 
                                       value="{% if action == 'edit' %}{{ printer.port }}{% else %}{{ form_data.port|default:'' }}{% endif %}"
                                       placeholder="9100">
                                <div class="form-text">رقم المنفذ (للطابعات الشبكية)</div>
                            </div>
                        </div>
                    </div>

                    <!-- Settings Section -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="bi bi-sliders"></i>
                            الإعدادات
                        </h3>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="is_active" 
                                           name="is_active"
                                           {% if action == 'edit' and printer.is_active %}checked
                                           {% elif action != 'edit' %}checked{% endif %}>
                                    <label class="form-check-label" for="is_active">
                                        <i class="bi bi-toggle-on me-1"></i>طابعة نشطة
                                    </label>
                                    <div class="form-text">تفعيل أو إلغاء تفعيل الطابعة</div>
                                </div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="is_default" 
                                           name="is_default"
                                           {% if action == 'edit' and printer.is_default %}checked{% endif %}>
                                    <label class="form-check-label" for="is_default">
                                        <i class="bi bi-star me-1"></i>طابعة افتراضية
                                    </label>
                                    <div class="form-text">جعل هذه الطابعة افتراضية للنظام</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="notes" class="form-label">
                                    <i class="bi bi-journal-text"></i>ملاحظات
                                </label>
                                <textarea class="form-control" 
                                          id="notes" 
                                          name="notes" 
                                          rows="3"
                                          placeholder="ملاحظات إضافية...">{% if action == 'edit' %}{{ printer.notes }}{% else %}{{ form_data.notes|default:'' }}{% endif %}</textarea>
                                <div class="form-text">ملاحظات إضافية (اختياري)</div>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <div>
                            <a href="{% url 'definitions:dashboard' %}" class="btn btn-outline-secondary me-2">
                                <i class="bi bi-house me-2"></i>التعريفات
                            </a>
                            <a href="{% url 'definitions:printer_list' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-right me-2"></i>قائمة الطابعات
                            </a>
                        </div>
                        
                        <button type="submit" class="btn btn-primary" onclick="this.disabled=true; this.innerHTML='<span class=\'spinner-border spinner-border-sm me-2\'></span>جاري الحفظ...'; this.form.submit();">
                            <i class="bi bi-check-circle me-2"></i>
                            {% if action == 'edit' %}
                            حفظ التعديلات
                            {% else %}
                            إضافة الطابعة
                            {% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تم تحميل صفحة الطابعة');
});

function selectRadio(element, name, value) {
    // إزالة التحديد من جميع الخيارات في نفس المجموعة
    const group = element.closest('.radio-group');
    group.querySelectorAll('.radio-option').forEach(option => {
        option.classList.remove('selected');
    });
    
    // إضافة التحديد للخيار المختار
    element.classList.add('selected');
    
    // تحديد الراديو بوتن
    element.querySelector('input[type="radio"]').checked = true;
}
</script>
{% endblock %}
