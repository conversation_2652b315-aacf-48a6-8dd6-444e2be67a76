from django import forms
from django.core.exceptions import ValidationError
from .models import (
    WarehouseDefinition, ProductDefinition, ProductCategory,
    CurrencyDefinition, BankDefinition, CashBoxDefinition,
    PersonDefinition, WarehouseLocation, AssetGroup, AssetBrand,
    ExpenseType, ExpenseName, RevenueType, RevenueName, ProfitCenter
)

# ===== نماذج أماكن الأصناف في المخازن =====

class WarehouseLocationForm(forms.ModelForm):
    """نموذج إضافة وتعديل أماكن الأصناف في المخازن"""

    class Meta:
        model = WarehouseLocation
        fields = [
            'warehouse', 'code', 'name', 'description',
            'max_capacity', 'max_weight', 'max_items',
            'current_capacity', 'current_weight', 'current_items',
            'is_active'
        ]

        widgets = {
            'warehouse': forms.Select(attrs={
                'class': 'form-select',
                'required': True
            }),
            'code': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'مثال: A1, B2, C3',
                'required': True
            }),
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'مثال: الرف الأول، المنطقة الشمالية',
                'required': True
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'وصف تفصيلي للمكان وخصائصه'
            }),
            'max_capacity': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
            'max_weight': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
            'max_items': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0',
                'placeholder': '0'
            }),
            'current_capacity': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00',
                'value': '0.00'
            }),
            'current_weight': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00',
                'value': '0.00'
            }),
            'current_items': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0',
                'placeholder': '0',
                'value': '0'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input',
                'checked': True
            })
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # تخصيص queryset للمخازن النشطة فقط
        self.fields['warehouse'].queryset = WarehouseDefinition.objects.filter(is_active=True)

        # إضافة خيار فارغ للمخزن
        self.fields['warehouse'].empty_label = "اختر المخزن"

        # تعيين القيم الافتراضية للحقول الحالية إذا كان هذا إنشاء جديد
        if not self.instance.pk:
            self.fields['current_capacity'].initial = 0
            self.fields['current_weight'].initial = 0
            self.fields['current_items'].initial = 0
            self.fields['is_active'].initial = True

        # جعل الحقول الحالية غير مطلوبة
        self.fields['current_capacity'].required = False
        self.fields['current_weight'].required = False
        self.fields['current_items'].required = False

    def clean_code(self):
        """التحقق من صحة كود المكان"""
        code = self.cleaned_data.get('code')
        warehouse = self.cleaned_data.get('warehouse')

        if code and warehouse:
            # التحقق من عدم تكرار الكود في نفس المخزن
            existing = WarehouseLocation.objects.filter(
                warehouse=warehouse,
                code=code
            )

            # استثناء الكائن الحالي في حالة التعديل
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)

            if existing.exists():
                raise ValidationError(f'يوجد مكان آخر بنفس الكود "{code}" في هذا المخزن')

        return code

    def clean_current_capacity(self):
        """التحقق من صحة السعة الحالية"""
        current_capacity = self.cleaned_data.get('current_capacity')

        # إذا كان الحقل فارغ، استخدم 0 كقيمة افتراضية
        if current_capacity is None or current_capacity == '':
            current_capacity = 0

        max_capacity = self.cleaned_data.get('max_capacity')

        if current_capacity < 0:
            raise ValidationError('السعة الحالية لا يمكن أن تكون سالبة')

        if max_capacity and current_capacity > max_capacity:
            raise ValidationError('السعة الحالية لا يمكن أن تتجاوز السعة القصوى')

        return current_capacity

    def clean_current_weight(self):
        """التحقق من صحة الوزن الحالي"""
        current_weight = self.cleaned_data.get('current_weight')

        # إذا كان الحقل فارغ، استخدم 0 كقيمة افتراضية
        if current_weight is None or current_weight == '':
            current_weight = 0

        max_weight = self.cleaned_data.get('max_weight')

        if current_weight < 0:
            raise ValidationError('الوزن الحالي لا يمكن أن يكون سالباً')

        if max_weight and current_weight > max_weight:
            raise ValidationError('الوزن الحالي لا يمكن أن يتجاوز الوزن الأقصى')

        return current_weight

    def clean_current_items(self):
        """التحقق من صحة عدد الأصناف الحالي"""
        current_items = self.cleaned_data.get('current_items')

        # إذا كان الحقل فارغ، استخدم 0 كقيمة افتراضية
        if current_items is None or current_items == '':
            current_items = 0

        max_items = self.cleaned_data.get('max_items')

        if current_items < 0:
            raise ValidationError('عدد الأصناف الحالي لا يمكن أن يكون سالباً')

        if max_items and current_items > max_items:
            raise ValidationError('عدد الأصناف الحالي لا يمكن أن يتجاوز العدد الأقصى')

        return current_items

    def clean(self):
        """التحقق الشامل من صحة البيانات"""
        cleaned_data = super().clean()

        # تعيين القيم الافتراضية للحقول المطلوبة إذا كانت فارغة
        if 'current_capacity' not in cleaned_data or cleaned_data['current_capacity'] is None:
            cleaned_data['current_capacity'] = 0

        if 'current_weight' not in cleaned_data or cleaned_data['current_weight'] is None:
            cleaned_data['current_weight'] = 0

        if 'current_items' not in cleaned_data or cleaned_data['current_items'] is None:
            cleaned_data['current_items'] = 0

        # التحقق من الحدود القصوى (اختياري الآن)
        max_capacity = cleaned_data.get('max_capacity')
        max_weight = cleaned_data.get('max_weight')
        max_items = cleaned_data.get('max_items')

        # إزالة التحقق الإجباري - يمكن إنشاء مكان بدون حدود قصوى
        # if not any([max_capacity, max_weight, max_items]):
        #     raise ValidationError(
        #         'يجب تحديد على الأقل حد واحد من الحدود (السعة، الوزن، أو عدد الأصناف)'
        #     )

        return cleaned_data

class ProductDefinitionForm(forms.ModelForm):
    """نموذج إضافة وتعديل تعريف الأصناف"""

    class Meta:
        model = ProductDefinition
        fields = [
            'code', 'barcode', 'name', 'description', 'category', 'product_type',
            'cost_price', 'selling_price', 'wholesale_price',
            'main_unit', 'minimum_stock', 'maximum_stock',
            'is_active', 'track_inventory', 'image'
        ]

        widgets = {
            'code': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'PRD001, ITM-2024-001',
                'required': True
            }),
            'barcode': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '1234567890123'
            }),
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم الصنف',
                'required': True
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'وصف تفصيلي للصنف ومواصفاته'
            }),
            'category': forms.Select(attrs={
                'class': 'form-select'
            }),
            'product_type': forms.Select(attrs={
                'class': 'form-select'
            }, choices=[
                ('product', 'منتج'),
                ('service', 'خدمة'),
                ('raw_material', 'مادة خام'),
                ('finished_goods', 'منتج تام'),
                ('semi_finished', 'منتج نصف مصنع'),
                ('consumable', 'مستهلكات'),
                ('spare_parts', 'قطع غيار'),
                ('tools', 'أدوات'),
                ('equipment', 'معدات'),
                ('software', 'برمجيات'),
            ]),
            'cost_price': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
            'selling_price': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
            'wholesale_price': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
            'main_unit': forms.Select(attrs={
                'class': 'form-select'
            }, choices=[
                ('piece', 'قطعة'),
                ('kg', 'كيلوجرام'),
                ('gram', 'جرام'),
                ('meter', 'متر'),
                ('cm', 'سنتيمتر'),
                ('liter', 'لتر'),
                ('ml', 'مليلتر'),
                ('box', 'علبة'),
                ('carton', 'كرتونة'),
                ('pack', 'عبوة'),
                ('bottle', 'زجاجة'),
                ('bag', 'كيس'),
                ('roll', 'لفة'),
                ('sheet', 'ورقة'),
                ('set', 'طقم'),
                ('pair', 'زوج'),
                ('dozen', 'دستة'),
                ('ton', 'طن'),
                ('hour', 'ساعة'),
                ('day', 'يوم'),
                ('month', 'شهر'),
                ('year', 'سنة'),
            ]),
            'minimum_stock': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
            'maximum_stock': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input',
                'checked': True
            }),
            'track_inventory': forms.CheckboxInput(attrs={
                'class': 'form-check-input',
                'checked': True
            }),
            'image': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*'
            })
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # تخصيص queryset للفئات النشطة فقط
        self.fields['category'].queryset = ProductCategory.objects.filter(is_active=True)
        self.fields['category'].empty_label = "اختر الفئة"
        self.fields['category'].required = False

        # تعيين القيم الافتراضية
        if not self.instance.pk:
            self.fields['minimum_stock'].initial = 0
            self.fields['maximum_stock'].initial = 100
            self.fields['is_active'].initial = True
            self.fields['track_inventory'].initial = True
            self.fields['product_type'].initial = 'product'
            self.fields['cost_price'].initial = 0
            self.fields['selling_price'].initial = 0

    def clean_code(self):
        """التحقق من صحة كود الصنف"""
        code = self.cleaned_data.get('code')

        if code:
            # التحقق من عدم تكرار الكود
            existing = ProductDefinition.objects.filter(code=code)

            # استثناء الكائن الحالي في حالة التعديل
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)

            if existing.exists():
                raise ValidationError(f'يوجد صنف آخر بنفس الكود "{code}"')

        return code

    def clean_barcode(self):
        """التحقق من صحة الباركود"""
        barcode = self.cleaned_data.get('barcode')

        # إذا كان الباركود فارغاً، إرجاع سلسلة فارغة بدلاً من None
        if not barcode:
            return ''

        # التحقق من عدم تكرار الباركود
        existing = ProductDefinition.objects.filter(barcode=barcode)

        # استثناء الكائن الحالي في حالة التعديل
        if self.instance.pk:
            existing = existing.exclude(pk=self.instance.pk)

        if existing.exists():
            raise ValidationError(f'يوجد صنف آخر بنفس الباركود "{barcode}"')

        return barcode

    def clean_selling_price(self):
        """التحقق من صحة سعر البيع"""
        selling_price = self.cleaned_data.get('selling_price')

        # السماح بسعر البيع = 0 للمواد الخام أو الخدمات المجانية
        if selling_price is not None and selling_price < 0:
            raise ValidationError('سعر البيع لا يمكن أن يكون سالباً')

        return selling_price

# تم حذف clean_current_stock لأن الحقل غير موجود

    def clean_minimum_stock(self):
        """التحقق من صحة الحد الأدنى للمخزون"""
        minimum_stock = self.cleaned_data.get('minimum_stock', 0)

        if minimum_stock < 0:
            raise ValidationError('الحد الأدنى للمخزون لا يمكن أن يكون سالباً')

        return minimum_stock

    def clean(self):
        """التحقق الشامل من صحة البيانات"""
        cleaned_data = super().clean()

        cost_price = cleaned_data.get('cost_price')
        selling_price = cleaned_data.get('selling_price')
        wholesale_price = cleaned_data.get('wholesale_price')
        minimum_stock = cleaned_data.get('minimum_stock')
        maximum_stock = cleaned_data.get('maximum_stock')

        # التحقق من أن سعر البيع أكبر من سعر التكلفة (إذا كان سعر البيع أكبر من 0)
        if cost_price and selling_price and selling_price > 0 and cost_price >= selling_price:
            raise ValidationError('سعر البيع يجب أن يكون أكبر من سعر التكلفة')

        # التحقق من أن سعر الجملة أقل من سعر البيع (إذا كان سعر البيع أكبر من 0)
        if wholesale_price and selling_price and selling_price > 0 and wholesale_price >= selling_price:
            raise ValidationError('سعر الجملة يجب أن يكون أقل من سعر البيع')

        # التحقق من أن الحد الأقصى أكبر من الحد الأدنى
        if minimum_stock and maximum_stock and minimum_stock >= maximum_stock:
            raise ValidationError('الحد الأقصى للمخزون يجب أن يكون أكبر من الحد الأدنى')

        return cleaned_data
