{% extends 'base.html' %}

{% block title %}تحويلات المخزون - أوساريك{% endblock %}

{% block content %}
    <div class="page-header d-flex justify-content-between align-items-center">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'dashboard_home' %}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'warehouses:dashboard' %}">المخازن</a></li>
                    <li class="breadcrumb-item active">تحويلات المخزون</li>
                </ol>
            </nav>
            <h1 class="page-title">تحويلات المخزون</h1>
            <p class="page-subtitle">إدارة تحويل البضائع بين المخازن المختلفة</p>
        </div>
        <a href="#" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>تحويل جديد
        </a>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-search"></i>
                        </span>
                        <input type="text" 
                               class="form-control" 
                               name="search" 
                               value="{{ search_query }}" 
                               placeholder="البحث في رقم التحويل أو المخازن...">
                    </div>
                </div>
                <div class="col-md-4">
                    <select name="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        {% for status_code, status_name in status_choices %}
                            <option value="{{ status_code }}" {% if status_filter == status_code %}selected{% endif %}>
                                {{ status_name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="bi bi-search"></i>
                        </button>
                        <a href="{% url 'warehouses:stock_transfers' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-clockwise"></i>
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Transfers Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">قائمة تحويلات المخزون</h5>
            <span class="badge bg-primary">{{ page_obj.paginator.count }} تحويل</span>
        </div>
        <div class="card-body">
            {% if page_obj %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>رقم التحويل</th>
                                <th>من المخزن</th>
                                <th>إلى المخزن</th>
                                <th>تاريخ التحويل</th>
                                <th>تاريخ الوصول المتوقع</th>
                                <th>الحالة</th>
                                <th>المستخدم</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transfer in page_obj %}
                                <tr>
                                    <td>
                                        <strong>{{ transfer.transfer_number }}</strong>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="warehouse-icon bg-danger text-white rounded me-2 d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                                                <i class="bi bi-arrow-up" style="font-size: 0.8rem;"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ transfer.from_warehouse.name }}</h6>
                                                <small class="text-muted">{{ transfer.from_warehouse.code }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="warehouse-icon bg-success text-white rounded me-2 d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                                                <i class="bi bi-arrow-down" style="font-size: 0.8rem;"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ transfer.to_warehouse.name }}</h6>
                                                <small class="text-muted">{{ transfer.to_warehouse.code }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ transfer.transfer_date|date:"Y/m/d" }}</strong>
                                            <br>
                                            <small class="text-muted">{{ transfer.transfer_date|date:"H:i" }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        {% if transfer.expected_arrival %}
                                            <div>
                                                <strong>{{ transfer.expected_arrival|date:"Y/m/d" }}</strong>
                                                <br>
                                                <small class="text-muted">{{ transfer.expected_arrival|date:"H:i" }}</small>
                                            </div>
                                        {% else %}
                                            <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if transfer.status == 'draft' %}
                                            <span class="badge bg-secondary">
                                                <i class="bi bi-file-earmark me-1"></i>مسودة
                                            </span>
                                        {% elif transfer.status == 'pending' %}
                                            <span class="badge bg-warning">
                                                <i class="bi bi-clock me-1"></i>في الانتظار
                                            </span>
                                        {% elif transfer.status == 'in_transit' %}
                                            <span class="badge bg-info">
                                                <i class="bi bi-truck me-1"></i>في الطريق
                                            </span>
                                        {% elif transfer.status == 'completed' %}
                                            <span class="badge bg-success">
                                                <i class="bi bi-check-circle me-1"></i>مكتمل
                                            </span>
                                        {% elif transfer.status == 'cancelled' %}
                                            <span class="badge bg-danger">
                                                <i class="bi bi-x-circle me-1"></i>ملغي
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ transfer.created_by }}</small>
                                        <br>
                                        <small class="text-muted">{{ transfer.created_at|date:"m/d H:i" }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" title="عرض التفاصيل">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                            {% if transfer.status == 'draft' or transfer.status == 'pending' %}
                                                <button class="btn btn-outline-success" title="تعديل">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                            {% endif %}
                                            {% if transfer.status == 'pending' %}
                                                <button class="btn btn-outline-info" title="تأكيد الإرسال">
                                                    <i class="bi bi-truck"></i>
                                                </button>
                                            {% endif %}
                                            {% if transfer.status == 'in_transit' %}
                                                <button class="btn btn-outline-success" title="تأكيد الاستلام">
                                                    <i class="bi bi-check-circle"></i>
                                                </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                    <nav aria-label="صفحات تحويلات المخزون" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">الأولى</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">السابقة</a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">
                                    صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">التالية</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">الأخيرة</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-truck text-muted" style="font-size: 4rem;"></i>
                    <h4 class="mt-3 text-muted">لا توجد تحويلات</h4>
                    <p class="text-muted">لم يتم العثور على أي تحويلات مطابقة لمعايير البحث.</p>
                    <a href="#" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>إنشاء تحويل جديد
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block extra_css %}
<style>
    .table-responsive {
        border-radius: 0.375rem;
    }
    
    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
    }
    
    .warehouse-icon {
        font-size: 0.8rem;
    }
    
    .breadcrumb {
        background-color: transparent;
        padding: 0;
        margin-bottom: 1rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        content: "←";
    }
    
    .badge {
        font-size: 0.75rem;
    }
    
    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-submit form when status filter changes
        const statusSelect = document.querySelector('select[name="status"]');
        
        if (statusSelect) {
            statusSelect.addEventListener('change', function() {
                this.form.submit();
            });
        }
    });
</script>
{% endblock %}
