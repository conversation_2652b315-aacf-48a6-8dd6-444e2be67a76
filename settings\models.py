from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator

class SystemSetting(models.Model):
    """إعدادات النظام"""
    SETTING_TYPES = [
        ('text', 'نص'),
        ('number', 'رقم'),
        ('boolean', 'صحيح/خطأ'),
        ('email', 'بريد إلكتروني'),
        ('url', 'رابط'),
        ('json', 'JSON'),
        ('file', 'ملف'),
    ]

    CATEGORIES = [
        ('general', 'عام'),
        ('company', 'بيانات الشركة'),
        ('financial', 'مالي'),
        ('inventory', 'مخزون'),
        ('sales', 'مبيعات'),
        ('purchases', 'مشتريات'),
        ('hr', 'موارد بشرية'),
        ('security', 'أمان'),
        ('notifications', 'إشعارات'),
        ('backup', 'نسخ احتياطي'),
        ('integration', 'تكامل'),
    ]

    category = models.CharField(max_length=20, choices=CATEGORIES, verbose_name="الفئة")
    key = models.CharField(max_length=100, unique=True, verbose_name="المفتاح")
    name = models.CharField(max_length=200, verbose_name="الاسم")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    setting_type = models.CharField(max_length=20, choices=SETTING_TYPES, verbose_name="نوع الإعداد")
    value = models.TextField(blank=True, null=True, verbose_name="القيمة")
    default_value = models.TextField(blank=True, null=True, verbose_name="القيمة الافتراضية")
    is_required = models.BooleanField(default=False, verbose_name="مطلوب")
    is_editable = models.BooleanField(default=True, verbose_name="قابل للتعديل")
    order = models.PositiveIntegerField(default=0, verbose_name="الترتيب")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "إعداد النظام"
        verbose_name_plural = "إعدادات النظام"
        ordering = ['category', 'order', 'name']

    def __str__(self):
        return f"{self.get_category_display()} - {self.name}"

    def get_value(self):
        """الحصول على القيمة مع التحويل المناسب"""
        if self.value is None:
            return self.default_value

        if self.setting_type == 'boolean':
            return self.value.lower() in ['true', '1', 'yes', 'on']
        elif self.setting_type == 'number':
            try:
                return float(self.value) if '.' in self.value else int(self.value)
            except (ValueError, TypeError):
                return 0
        elif self.setting_type == 'json':
            import json
            try:
                return json.loads(self.value)
            except (json.JSONDecodeError, TypeError):
                return {}
        else:
            return self.value

class UserPreference(models.Model):
    """تفضيلات المستخدم"""
    PREFERENCE_TYPES = [
        ('theme', 'المظهر'),
        ('language', 'اللغة'),
        ('timezone', 'المنطقة الزمنية'),
        ('notifications', 'الإشعارات'),
        ('dashboard', 'لوحة التحكم'),
        ('reports', 'التقارير'),
    ]

    user = models.ForeignKey(User, related_name='preferences', on_delete=models.CASCADE, verbose_name="المستخدم")
    preference_type = models.CharField(max_length=20, choices=PREFERENCE_TYPES, verbose_name="نوع التفضيل")
    key = models.CharField(max_length=100, verbose_name="المفتاح")
    value = models.TextField(verbose_name="القيمة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "تفضيل المستخدم"
        verbose_name_plural = "تفضيلات المستخدمين"
        unique_together = ['user', 'key']
        ordering = ['user', 'preference_type', 'key']

    def __str__(self):
        return f"{self.user.username} - {self.key}"

class BackupSchedule(models.Model):
    """جدولة النسخ الاحتياطية"""
    FREQUENCY_CHOICES = [
        ('daily', 'يومي'),
        ('weekly', 'أسبوعي'),
        ('monthly', 'شهري'),
    ]

    STATUS_CHOICES = [
        ('active', 'نشط'),
        ('inactive', 'غير نشط'),
        ('paused', 'متوقف مؤقتاً'),
    ]

    name = models.CharField(max_length=200, verbose_name="اسم الجدولة")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    frequency = models.CharField(max_length=20, choices=FREQUENCY_CHOICES, verbose_name="التكرار")
    backup_time = models.TimeField(verbose_name="وقت النسخ الاحتياطي")
    include_database = models.BooleanField(default=True, verbose_name="تضمين قاعدة البيانات")
    include_media = models.BooleanField(default=True, verbose_name="تضمين الملفات")
    retention_days = models.PositiveIntegerField(default=30, verbose_name="أيام الاحتفاظ")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name="الحالة")
    last_backup = models.DateTimeField(null=True, blank=True, verbose_name="آخر نسخة احتياطية")
    next_backup = models.DateTimeField(null=True, blank=True, verbose_name="النسخة الاحتياطية التالية")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "جدولة نسخة احتياطية"
        verbose_name_plural = "جدولة النسخ الاحتياطية"
        ordering = ['name']

    def __str__(self):
        return self.name

class BackupHistory(models.Model):
    """تاريخ النسخ الاحتياطية"""
    STATUS_CHOICES = [
        ('running', 'قيد التنفيذ'),
        ('completed', 'مكتمل'),
        ('failed', 'فشل'),
    ]

    schedule = models.ForeignKey(BackupSchedule, related_name='backups', on_delete=models.CASCADE,
                                null=True, blank=True, verbose_name="الجدولة")
    backup_date = models.DateTimeField(verbose_name="تاريخ النسخة الاحتياطية")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, verbose_name="الحالة")
    file_path = models.CharField(max_length=500, blank=True, null=True, verbose_name="مسار الملف")
    file_size = models.BigIntegerField(default=0, verbose_name="حجم الملف (بايت)")
    duration = models.DurationField(null=True, blank=True, verbose_name="مدة النسخ")
    error_message = models.TextField(blank=True, null=True, verbose_name="رسالة الخطأ")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "تاريخ نسخة احتياطية"
        verbose_name_plural = "تاريخ النسخ الاحتياطية"
        ordering = ['-backup_date']

    def __str__(self):
        return f"نسخة احتياطية - {self.backup_date.strftime('%Y-%m-%d %H:%M')}"

class SystemLog(models.Model):
    """سجلات النظام"""
    LOG_LEVELS = [
        ('debug', 'تصحيح'),
        ('info', 'معلومات'),
        ('warning', 'تحذير'),
        ('error', 'خطأ'),
        ('critical', 'حرج'),
    ]

    level = models.CharField(max_length=20, choices=LOG_LEVELS, verbose_name="المستوى")
    message = models.TextField(verbose_name="الرسالة")
    module = models.CharField(max_length=100, blank=True, null=True, verbose_name="الوحدة")
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="المستخدم")
    ip_address = models.GenericIPAddressField(null=True, blank=True, verbose_name="عنوان IP")
    user_agent = models.TextField(blank=True, null=True, verbose_name="وكيل المستخدم")
    extra_data = models.JSONField(default=dict, blank=True, verbose_name="بيانات إضافية")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "سجل النظام"
        verbose_name_plural = "سجلات النظام"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.get_level_display()} - {self.message[:50]}"
