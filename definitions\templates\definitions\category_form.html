{% extends 'base.html' %}

{% block title %}{% if action == 'create' %}إضافة فئة أصناف جديدة{% else %}تعديل فئة الأصناف{% endif %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1>{% if action == 'create' %}إضافة فئة أصناف جديدة{% else %}تعديل فئة الأصناف{% endif %}</h1>
            <p class="text-muted">
                {% if action == 'create' %}إنشاء تصنيف جديد للأصناف في النظام{% else %}تحديث بيانات فئة الأصناف الموجودة{% endif %}
            </p>
        </div>
        <div>
            <a href="{% url 'definitions:category_list' %}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-2"></i>عودة للقائمة
            </a>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-tags me-2"></i>بيانات فئة الأصناف
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <!-- Basic Information -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="code" class="form-label">
                                    كود الفئة <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       id="code" 
                                       name="code" 
                                       class="form-control" 
                                       value="{% if category %}{{ category.code }}{% elif form_data %}{{ form_data.code }}{% endif %}"
                                       required 
                                       placeholder="مثال: ELEC">
                                <div class="form-text">كود فريد للفئة (أحرف وأرقام فقط)</div>
                            </div>
                            <div class="col-md-6">
                                <label for="name" class="form-label">
                                    اسم الفئة <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       id="name" 
                                       name="name" 
                                       class="form-control" 
                                       value="{% if category %}{{ category.name }}{% elif form_data %}{{ form_data.name }}{% endif %}"
                                       required 
                                       placeholder="مثال: الإلكترونيات">
                                <div class="form-text">الاسم الكامل للفئة</div>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="parent_category" class="form-label">الفئة الأب</label>
                                <select id="parent_category" name="parent_category" class="form-select">
                                    <option value="">فئة رئيسية (بدون أب)</option>
                                    {% for parent in parent_categories %}
                                    <option value="{{ parent.id }}" 
                                            {% if category and category.parent and category.parent.id == parent.id %}selected{% elif form_data and form_data.parent_category == parent.id|stringformat:"s" %}selected{% endif %}>
                                        {{ parent.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">اختر الفئة الأب إذا كانت هذه فئة فرعية</div>
                            </div>
                            <div class="col-md-6">
                                <label for="is_active" class="form-label">حالة الفئة</label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="is_active" 
                                           name="is_active"
                                           {% if category %}{% if category.is_active %}checked{% endif %}{% elif form_data %}{% if form_data.is_active %}checked{% endif %}{% else %}checked{% endif %}>
                                    <label class="form-check-label" for="is_active">
                                        الفئة نشطة ومتاحة للاستخدام
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-12">
                                <label for="description" class="form-label">وصف الفئة</label>
                                <textarea id="description" 
                                          name="description" 
                                          class="form-control" 
                                          rows="4" 
                                          placeholder="وصف تفصيلي عن فئة الأصناف ونوع المنتجات التي تحتويها...">{% if category %}{{ category.description }}{% elif form_data %}{{ form_data.description }}{% endif %}</textarea>
                                <div class="form-text">وصف مفصل عن الفئة ونوع الأصناف التي تحتويها</div>
                            </div>
                        </div>

                        <!-- Category Hierarchy Info -->
                        {% if category and category.parent %}
                        <div class="alert alert-info">
                            <h6 class="alert-heading">
                                <i class="bi bi-info-circle me-2"></i>معلومات التسلسل الهرمي
                            </h6>
                            <p class="mb-0">
                                هذه الفئة فرعية من: <strong>{{ category.parent.name }}</strong>
                            </p>
                        </div>
                        {% endif %}

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{% url 'definitions:category_list' %}" class="btn btn-secondary">
                                <i class="bi bi-x-circle me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-{% if action == 'create' %}plus-circle{% else %}check-circle{% endif %} me-2"></i>
                                {% if action == 'create' %}إضافة الفئة{% else %}حفظ التغييرات{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تنظيف كود الفئة
    const codeInput = document.getElementById('code');
    if (codeInput) {
        codeInput.addEventListener('input', function() {
            this.value = this.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
        });
    }
    
    // معلومات عن أنواع الفئات
    const parentSelect = document.getElementById('parent_category');
    if (parentSelect) {
        parentSelect.addEventListener('change', function() {
            const infoDiv = document.getElementById('category-info');
            if (this.value) {
                if (!infoDiv) {
                    const info = document.createElement('div');
                    info.id = 'category-info';
                    info.className = 'alert alert-info mt-2';
                    info.innerHTML = '<i class="bi bi-info-circle me-2"></i>ستكون هذه فئة فرعية تحت الفئة المختارة';
                    this.parentElement.appendChild(info);
                }
            } else {
                if (infoDiv) {
                    infoDiv.remove();
                }
            }
        });
    }
});
</script>
{% endblock %}
