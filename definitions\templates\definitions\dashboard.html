{% extends 'base.html' %}
{% load static %}

{% block title %}مركز التعريفات الذكي{% endblock %}

{% block extra_css %}
<style>
    /* Advanced Definitions Dashboard Styles */
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --danger-gradient: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
        --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --glass-bg: rgba(255, 255, 255, 0.1);
        --glass-border: rgba(255, 255, 255, 0.2);
        --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
        --shadow-heavy: 0 15px 35px rgba(31, 38, 135, 0.2);
    }

    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
    }

    /* Hero Section with Glass Morphism */
    .definitions-hero {
        background: var(--primary-gradient);
        padding: 4rem 0;
        margin-bottom: 3rem;
        position: relative;
        overflow: hidden;
        border-radius: 0 0 2rem 2rem;
    }

    .definitions-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    }

    .definitions-hero::after {
        content: '';
        position: absolute;
        top: -50%;
        right: -20%;
        width: 100%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        transform: rotate(45deg);
    }

    .hero-content {
        position: relative;
        z-index: 2;
        color: white;
    }

    .hero-title {
        font-size: 3.5rem;
        font-weight: 800;
        margin-bottom: 1rem;
        text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        background: linear-gradient(45deg, #fff, #e0e7ff);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .hero-subtitle {
        font-size: 1.3rem;
        opacity: 0.9;
        margin-bottom: 2rem;
        font-weight: 300;
    }

    /* Glass Cards */
    .glass-card {
        background: var(--glass-bg);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid var(--glass-border);
        border-radius: 20px;
        padding: 2rem;
        box-shadow: var(--shadow-light);
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        overflow: hidden;
    }

    .glass-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    }

    .glass-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: var(--shadow-heavy);
        background: rgba(255, 255, 255, 0.15);
    }

    /* Advanced Statistics Cards */
    .stat-card {
        background: white;
        border-radius: 24px;
        padding: 2.5rem;
        box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        border: none;
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
        height: 100%;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--primary-gradient);
    }

    .stat-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 60px rgba(0,0,0,0.15);
    }

    .stat-icon {
        width: 80px;
        height: 80px;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        margin-bottom: 1.5rem;
        position: relative;
        overflow: hidden;
    }

    .stat-icon::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: inherit;
        opacity: 0.1;
        border-radius: inherit;
    }

    .stat-number {
        font-size: 3rem;
        font-weight: 800;
        margin-bottom: 0.5rem;
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .stat-label {
        color: #64748b;
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .stat-change {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.9rem;
        font-weight: 600;
    }

    .stat-change.positive {
        color: #10b981;
    }

    .stat-change.negative {
        color: #ef4444;
    }

    /* Action Buttons */
    .action-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .action-card {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        text-decoration: none;
        color: inherit;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
        border: 1px solid #e5e7eb;
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .action-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--primary-gradient);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .action-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        text-decoration: none;
        color: white;
    }

    .action-card:hover::before {
        opacity: 1;
    }

    .action-card > * {
        position: relative;
        z-index: 2;
    }

    .action-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        flex-shrink: 0;
        transition: all 0.3s ease;
    }

    .action-content {
        flex: 1;
    }

    .action-title {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    .action-desc {
        font-size: 0.8rem;
        opacity: 0.7;
        margin-bottom: 0;
        line-height: 1.3;
    }

    .badge {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        z-index: 3;
    }

    /* Category Cards */
    .card {
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    .card-header {
        border-bottom: none;
        font-weight: 600;
    }

    /* Statistics Items */
    .stat-item h3 {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }

    .stat-item small {
        font-size: 0.8rem;
        font-weight: 500;
    }

    /* Additional Gradients */
    :root {
        --secondary-gradient: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    }

    /* Advanced Data Tables */
    .data-table {
        background: white;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        border: none;
    }

    .data-table .table {
        margin-bottom: 0;
    }

    .data-table .table thead th {
        background: #f8fafc;
        border: none;
        font-weight: 700;
        color: #374151;
        padding: 1.5rem 1rem;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .data-table .table tbody td {
        border: none;
        padding: 1.25rem 1rem;
        vertical-align: middle;
        border-bottom: 1px solid #f1f5f9;
    }

    .data-table .table tbody tr:hover {
        background: #f8fafc;
    }

    /* Animated Progress Bars */
    .progress-modern {
        height: 8px;
        border-radius: 10px;
        background: #e5e7eb;
        overflow: hidden;
        margin-top: 0.5rem;
    }

    .progress-modern .progress-bar {
        background: var(--primary-gradient);
        border-radius: 10px;
        transition: width 1s ease-in-out;
        position: relative;
    }

    .progress-modern .progress-bar::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.3) 50%, transparent 70%);
        animation: shimmer 2s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .hero-title {
            font-size: 2.5rem;
        }
        
        .action-grid {
            grid-template-columns: 1fr;
        }
        
        .stat-card {
            padding: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid" style="margin-top: 2rem;">


    <!-- Definitions Categories -->
    <div class="row mb-5">
        <div class="col-12">
            <h2 class="text-center mb-4" style="color: #374151; font-weight: 700;">
                <i class="bi bi-grid-3x3-gap me-2"></i>
                فئات التعريفات
            </h2>
        </div>
    </div>

    <!-- Main Categories Grid -->
    <div class="row g-4 mb-5">
        <!-- تعريف المخازن والمواقع -->
        <div class="col-lg-4 col-md-6">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-building me-2"></i>
                        المخازن والمواقع
                    </h5>
                </div>
                <div class="card-body">
                    <div class="action-grid">
                        <a href="{% url 'definitions:warehouse_list' %}" class="action-card">
                            <div class="action-icon" style="background: var(--info-gradient);">
                                <i class="bi bi-building text-white"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">تعريف المخازن</div>
                                <div class="action-desc">إدارة المخازن الرئيسية والفرعية</div>
                            </div>
                            <div class="badge bg-info">{{ stats.warehouses }}</div>
                        </a>

                        <a href="{% url 'definitions:warehouse_location_list' %}" class="action-card">
                            <div class="action-icon" style="background: var(--success-gradient);">
                                <i class="bi bi-geo-alt text-white"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">أماكن الأصناف</div>
                                <div class="action-desc">تحديد مواقع الأصناف داخل المخازن</div>
                            </div>
                            <div class="badge bg-success">{{ stats.warehouse_locations }}</div>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- تعريف الأصناف -->
        <div class="col-lg-4 col-md-6">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-box me-2"></i>
                        الأصناف والمنتجات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="action-grid">
                        <a href="{% url 'definitions:product_list' %}" class="action-card">
                            <div class="action-icon" style="background: var(--success-gradient);">
                                <i class="bi bi-box text-white"></i>
                            </div>
                            <div class="action-title">تعريف الأصناف</div>
                            <div class="action-desc">إدارة كتالوج المنتجات والأصناف</div>
                            <div class="badge bg-success">{{ stats.products }}</div>
                        </a>

                        <a href="{% url 'definitions:category_list' %}" class="action-card">
                            <div class="action-icon" style="background: var(--warning-gradient);">
                                <i class="bi bi-tags text-white"></i>
                            </div>
                            <div class="action-title">فئات الأصناف</div>
                            <div class="action-desc">تصنيف وتنظيم الأصناف</div>
                            <div class="badge bg-warning">{{ stats.categories }}</div>
                        </a>

                        <a href="#" class="action-card">
                            <div class="action-icon" style="background: var(--info-gradient);">
                                <i class="bi bi-upc-scan text-white"></i>
                            </div>
                            <div class="action-title">أكواد الأصناف</div>
                            <div class="action-desc">إدارة أكواد وباركود الأصناف</div>
                            <div class="badge bg-info">{{ stats.products }}</div>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- تعريف الأشخاص -->
        <div class="col-lg-4 col-md-6">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-header bg-warning text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-people me-2"></i>
                        الأشخاص والعلاقات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="action-grid">
                        <a href="#" class="action-card">
                            <div class="action-icon" style="background: var(--success-gradient);">
                                <i class="bi bi-person-check text-white"></i>
                            </div>
                            <div class="action-title">العملاء</div>
                            <div class="action-desc">إدارة بيانات العملاء</div>
                            <div class="badge bg-success">{{ stats.customers }}</div>
                        </a>

                        <a href="#" class="action-card">
                            <div class="action-icon" style="background: var(--info-gradient);">
                                <i class="bi bi-building text-white"></i>
                            </div>
                            <div class="action-title">الموردين</div>
                            <div class="action-desc">إدارة بيانات الموردين</div>
                            <div class="badge bg-info">{{ stats.suppliers }}</div>
                        </a>

                        <a href="#" class="action-card">
                            <div class="action-icon" style="background: var(--warning-gradient);">
                                <i class="bi bi-person-badge text-white"></i>
                            </div>
                            <div class="action-title">الموظفين</div>
                            <div class="action-desc">إدارة بيانات الموظفين</div>
                            <div class="badge bg-warning">{{ stats.employees }}</div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Secondary Categories Grid -->
    <div class="row g-4 mb-5">
        <!-- البنوك والعملات -->
        <div class="col-lg-4 col-md-6">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-bank me-2"></i>
                        البنوك والعملات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="action-grid">
                        <a href="{% url 'definitions:bank_list' %}" class="action-card">
                            <div class="action-icon" style="background: var(--info-gradient);">
                                <i class="bi bi-bank text-white"></i>
                            </div>
                            <div class="action-title">البنوك</div>
                            <div class="action-desc">تعريف البنوك والفروع المصرفية</div>
                            <div class="badge bg-info">{{ stats.banks }}</div>
                        </a>

                        <a href="{% url 'definitions:currency_list' %}" class="action-card">
                            <div class="action-icon" style="background: var(--warning-gradient);">
                                <i class="bi bi-currency-exchange text-white"></i>
                            </div>
                            <div class="action-title">العملات</div>
                            <div class="action-desc">إدارة العملات وأسعار الصرف</div>
                            <div class="badge bg-warning">{{ stats.currencies }}</div>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- وحدات القياس والأصول -->
        <div class="col-lg-4 col-md-6">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-rulers me-2"></i>
                        القياس والأصول
                    </h5>
                </div>
                <div class="card-body">
                    <div class="action-grid">
                        <a href="#" class="action-card">
                            <div class="action-icon" style="background: var(--secondary-gradient);">
                                <i class="bi bi-rulers text-white"></i>
                            </div>
                            <div class="action-title">وحدات القياس</div>
                            <div class="action-desc">تعريف وحدات القياس المختلفة</div>
                            <div class="badge bg-secondary">15</div>
                        </a>

                        <a href="#" class="action-card">
                            <div class="action-icon" style="background: var(--danger-gradient);">
                                <i class="bi bi-collection text-white"></i>
                            </div>
                            <div class="action-title">مجموعات الأصول</div>
                            <div class="action-desc">تصنيف الأصول الثابتة</div>
                            <div class="badge bg-danger">{{ stats.asset_groups }}</div>
                        </a>

                        <a href="#" class="action-card">
                            <div class="action-icon" style="background: var(--success-gradient);">
                                <i class="bi bi-award text-white"></i>
                            </div>
                            <div class="action-title">ماركات الأصول</div>
                            <div class="action-desc">تعريف ماركات وعلامات الأصول</div>
                            <div class="badge bg-success">{{ stats.asset_brands }}</div>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- المصروفات والإيرادات -->
        <div class="col-lg-4 col-md-6">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-cash-stack me-2"></i>
                        المصروفات والإيرادات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="action-grid">
                        <a href="#" class="action-card">
                            <div class="action-icon" style="background: var(--danger-gradient);">
                                <i class="bi bi-arrow-down-circle text-white"></i>
                            </div>
                            <div class="action-title">أنواع المصروفات</div>
                            <div class="action-desc">تصنيف أنواع المصروفات</div>
                            <div class="badge bg-danger">{{ stats.expense_types }}</div>
                        </a>

                        <a href="#" class="action-card">
                            <div class="action-icon" style="background: var(--warning-gradient);">
                                <i class="bi bi-receipt text-white"></i>
                            </div>
                            <div class="action-title">أسماء المصروفات</div>
                            <div class="action-desc">تفصيل أسماء المصروفات</div>
                            <div class="badge bg-warning">{{ stats.expense_names }}</div>
                        </a>

                        <a href="#" class="action-card">
                            <div class="action-icon" style="background: var(--success-gradient);">
                                <i class="bi bi-arrow-up-circle text-white"></i>
                            </div>
                            <div class="action-title">أنواع الإيرادات</div>
                            <div class="action-desc">تصنيف أنواع الإيرادات</div>
                            <div class="badge bg-success">{{ stats.revenue_types }}</div>
                        </a>

                        <a href="#" class="action-card">
                            <div class="action-icon" style="background: var(--info-gradient);">
                                <i class="bi bi-cash text-white"></i>
                            </div>
                            <div class="action-title">أسماء الإيرادات</div>
                            <div class="action-desc">تفصيل أسماء الإيرادات</div>
                            <div class="badge bg-info">{{ stats.revenue_names }}</div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Categories Grid -->
    <div class="row g-4 mb-5">
        <!-- مراكز الربحية والطابعات -->
        <div class="col-lg-6 col-md-12">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-graph-up me-2"></i>
                        مراكز الربحية والأجهزة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="action-grid">
                        <a href="#" class="action-card">
                            <div class="action-icon" style="background: var(--success-gradient);">
                                <i class="bi bi-graph-up text-white"></i>
                            </div>
                            <div class="action-title">مراكز الربحية</div>
                            <div class="action-desc">تعريف وإدارة مراكز الربحية</div>
                            <div class="badge bg-success">{{ stats.profit_centers }}</div>
                        </a>

                        <a href="#" class="action-card">
                            <div class="action-icon" style="background: var(--info-gradient);">
                                <i class="bi bi-printer text-white"></i>
                            </div>
                            <div class="action-title">تعريف الطابعات</div>
                            <div class="action-desc">إعداد وتكوين الطابعات</div>
                            <div class="badge bg-info">5</div>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="col-lg-6 col-md-12">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-speedometer2 me-2"></i>
                        إحصائيات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="stat-item">
                                <h3 class="text-primary">{{ stats.persons }}</h3>
                                <small class="text-muted">إجمالي الأشخاص</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-item">
                                <h3 class="text-success">{{ stats.products }}</h3>
                                <small class="text-muted">إجمالي الأصناف</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-item">
                                <h3 class="text-info">{{ stats.warehouses }}</h3>
                                <small class="text-muted">إجمالي المخازن</small>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="text-center">
                        <a href="#" class="btn btn-primary btn-sm">
                            <i class="bi bi-graph-up me-1"></i>
                            عرض التقرير الشامل
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
