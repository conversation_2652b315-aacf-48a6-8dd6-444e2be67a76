{% extends 'base.html' %}
{% load static %}

{% block title %}
{% if object %}تعديل مكان الصنف{% else %}إضافة مكان جديد{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    /* Advanced Form Styles */
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --danger-gradient: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
        --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
    }

    /* Hero Section */
    .form-hero {
        background: var(--primary-gradient);
        padding: 2rem 0;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
        border-radius: 0 0 2rem 2rem;
    }

    .form-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    }

    .hero-content {
        position: relative;
        z-index: 2;
        color: white;
        text-align: center;
    }

    .hero-title {
        font-size: 2rem;
        font-weight: 800;
        margin-bottom: 0.5rem;
        text-shadow: 0 4px 20px rgba(0,0,0,0.3);
    }

    .hero-subtitle {
        font-size: 1rem;
        opacity: 0.9;
        margin-bottom: 0;
    }

    /* Form Container */
    .form-container {
        background: white;
        border-radius: 20px;
        padding: 2.5rem;
        box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        border: 1px solid #e5e7eb;
        position: relative;
        overflow: hidden;
    }

    .form-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--primary-gradient);
    }

    /* Form Sections */
    .form-section {
        margin-bottom: 2rem;
        padding: 1.5rem;
        background: #f8fafc;
        border-radius: 12px;
        border: 1px solid #e5e7eb;
    }

    .section-title {
        font-size: 1.2rem;
        font-weight: 700;
        color: #374151;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .section-icon {
        width: 30px;
        height: 30px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 0.9rem;
    }

    /* Form Controls */
    .form-label {
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.3rem;
    }

    .form-control, .form-select {
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        padding: 0.75rem 1rem;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        background: white;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        background: white;
    }

    .form-control.is-invalid, .form-select.is-invalid {
        border-color: #ef4444;
        box-shadow: 0 0 0 0.2rem rgba(239, 68, 68, 0.25);
    }

    .invalid-feedback {
        display: block;
        color: #ef4444;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    /* Input Groups */
    .input-group {
        position: relative;
    }

    .input-group-text {
        background: var(--primary-gradient);
        color: white;
        border: none;
        border-radius: 12px 0 0 12px;
        font-weight: 600;
    }

    .input-group .form-control {
        border-radius: 0 12px 12px 0;
        border-left: none;
    }

    /* Capacity Indicators */
    .capacity-indicator {
        background: #f8fafc;
        border: 2px dashed #d1d5db;
        border-radius: 12px;
        padding: 1rem;
        text-align: center;
        margin-top: 1rem;
    }

    .capacity-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: #374151;
    }

    .capacity-label {
        font-size: 0.9rem;
        color: #6b7280;
        margin-top: 0.25rem;
    }

    /* Action Buttons */
    .form-actions {
        background: #f8fafc;
        border-radius: 12px;
        padding: 1.5rem;
        margin-top: 2rem;
        text-align: center;
        border: 1px solid #e5e7eb;
    }

    .btn-primary {
        background: var(--primary-gradient);
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 12px;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        background: var(--primary-gradient);
    }

    .btn-secondary {
        background: #6b7280;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 12px;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        color: white;
    }

    .btn-secondary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(107, 114, 128, 0.3);
        background: #4b5563;
        color: white;
    }

    /* Help Text */
    .form-text {
        color: #6b7280;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    /* Required Field Indicator */
    .required::after {
        content: ' *';
        color: #ef4444;
        font-weight: bold;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .form-container {
            padding: 1.5rem;
            margin: 1rem;
        }
        
        .form-section {
            padding: 1rem;
        }
        
        .hero-title {
            font-size: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="form-hero">
    <div class="container">
        <div class="hero-content">
            <h1 class="hero-title">
                <i class="bi bi-geo-alt me-2"></i>
                {% if object %}تعديل مكان الصنف{% else %}إضافة مكان جديد{% endif %}
            </h1>
            <p class="hero-subtitle">
                {% if object %}تحديث بيانات مكان الصنف في المخزن{% else %}إنشاء مكان جديد لتخزين الأصناف{% endif %}
            </p>
        </div>
    </div>
</div>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="form-container">
                <form method="post" novalidate id="locationForm">
                    {% csrf_token %}
                    
                    <!-- Basic Information Section -->
                    <div class="form-section">
                        <div class="section-title">
                            <div class="section-icon" style="background: var(--primary-gradient);">
                                <i class="bi bi-info-circle"></i>
                            </div>
                            المعلومات الأساسية
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.warehouse.id_for_label }}" class="form-label required">
                                        <i class="bi bi-building"></i>
                                        المخزن
                                    </label>
                                    {{ form.warehouse }}
                                    {% if form.warehouse.errors %}
                                        <div class="invalid-feedback">{{ form.warehouse.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">اختر المخزن الذي سيحتوي على هذا المكان</div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.code.id_for_label }}" class="form-label required">
                                        <i class="bi bi-hash"></i>
                                        كود المكان
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-hash"></i>
                                        </span>
                                        {{ form.code }}
                                    </div>
                                    {% if form.code.errors %}
                                        <div class="invalid-feedback">{{ form.code.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">كود فريد لتحديد المكان (مثل: A1, B2, C3)</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="{{ form.name.id_for_label }}" class="form-label required">
                                        <i class="bi bi-tag"></i>
                                        اسم المكان
                                    </label>
                                    {{ form.name }}
                                    {% if form.name.errors %}
                                        <div class="invalid-feedback">{{ form.name.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">اسم وصفي للمكان (مثل: الرف الأول، المنطقة الشمالية)</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="{{ form.description.id_for_label }}" class="form-label">
                                        <i class="bi bi-file-text"></i>
                                        الوصف
                                    </label>
                                    {{ form.description }}
                                    {% if form.description.errors %}
                                        <div class="invalid-feedback">{{ form.description.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">وصف تفصيلي للمكان وخصائصه</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Capacity Limits Section -->
                    <div class="form-section">
                        <div class="section-title">
                            <div class="section-icon" style="background: var(--warning-gradient);">
                                <i class="bi bi-speedometer2"></i>
                            </div>
                            حدود السعة
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.max_capacity.id_for_label }}" class="form-label">
                                        <i class="bi bi-box"></i>
                                        السعة القصوى
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-box"></i>
                                        </span>
                                        {{ form.max_capacity }}
                                        <span class="input-group-text">وحدة</span>
                                    </div>
                                    {% if form.max_capacity.errors %}
                                        <div class="invalid-feedback">{{ form.max_capacity.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">الحد الأقصى للسعة التخزينية</div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.max_weight.id_for_label }}" class="form-label">
                                        <i class="bi bi-speedometer"></i>
                                        الوزن الأقصى
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-speedometer"></i>
                                        </span>
                                        {{ form.max_weight }}
                                        <span class="input-group-text">كجم</span>
                                    </div>
                                    {% if form.max_weight.errors %}
                                        <div class="invalid-feedback">{{ form.max_weight.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">الحد الأقصى للوزن المسموح</div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.max_items.id_for_label }}" class="form-label">
                                        <i class="bi bi-collection"></i>
                                        عدد الأصناف الأقصى
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-collection"></i>
                                        </span>
                                        {{ form.max_items }}
                                        <span class="input-group-text">صنف</span>
                                    </div>
                                    {% if form.max_items.errors %}
                                        <div class="invalid-feedback">{{ form.max_items.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">الحد الأقصى لعدد الأصناف المختلفة</div>
                                </div>
                            </div>
                        </div>

                        {% if object %}
                        <div class="row">
                            <div class="col-md-4">
                                <div class="capacity-indicator">
                                    <div class="capacity-value text-info">{{ object.current_capacity|floatformat:1 }}</div>
                                    <div class="capacity-label">السعة الحالية</div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="capacity-indicator">
                                    <div class="capacity-value text-warning">{{ object.current_weight|floatformat:1 }}</div>
                                    <div class="capacity-label">الوزن الحالي (كجم)</div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="capacity-indicator">
                                    <div class="capacity-value text-success">{{ object.current_items }}</div>
                                    <div class="capacity-label">عدد الأصناف الحالي</div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Current Status Section -->
                    {% if object %}
                    <div class="form-section">
                        <div class="section-title">
                            <div class="section-icon" style="background: var(--info-gradient);">
                                <i class="bi bi-graph-up"></i>
                            </div>
                            الحالة الحالية
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.current_capacity.id_for_label }}" class="form-label">
                                        <i class="bi bi-box-fill"></i>
                                        السعة الحالية
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-box-fill"></i>
                                        </span>
                                        {{ form.current_capacity }}
                                        <span class="input-group-text">وحدة</span>
                                    </div>
                                    {% if form.current_capacity.errors %}
                                        <div class="invalid-feedback">{{ form.current_capacity.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">السعة المستخدمة حالياً</div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.current_weight.id_for_label }}" class="form-label">
                                        <i class="bi bi-speedometer2"></i>
                                        الوزن الحالي
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-speedometer2"></i>
                                        </span>
                                        {{ form.current_weight }}
                                        <span class="input-group-text">كجم</span>
                                    </div>
                                    {% if form.current_weight.errors %}
                                        <div class="invalid-feedback">{{ form.current_weight.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">الوزن المستخدم حالياً</div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.current_items.id_for_label }}" class="form-label">
                                        <i class="bi bi-collection-fill"></i>
                                        عدد الأصناف الحالي
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-collection-fill"></i>
                                        </span>
                                        {{ form.current_items }}
                                        <span class="input-group-text">صنف</span>
                                    </div>
                                    {% if form.current_items.errors %}
                                        <div class="invalid-feedback">{{ form.current_items.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">عدد الأصناف المختلفة حالياً</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Settings Section -->
                    <div class="form-section">
                        <div class="section-title">
                            <div class="section-icon" style="background: var(--success-gradient);">
                                <i class="bi bi-gear"></i>
                            </div>
                            الإعدادات
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-check form-switch">
                                    {{ form.is_active }}
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        <i class="bi bi-toggle-on text-success"></i>
                                        المكان نشط ومتاح للاستخدام
                                    </label>
                                    {% if form.is_active.errors %}
                                        <div class="invalid-feedback">{{ form.is_active.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">عند إلغاء التفعيل، لن يكون المكان متاحاً لتخزين أصناف جديدة</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="form-actions">
                        <div class="d-flex justify-content-center gap-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-2"></i>
                                {% if object %}تحديث المكان{% else %}إضافة المكان{% endif %}
                            </button>
                            <a href="{% url 'definitions:warehouse_location_list' %}" class="btn btn-secondary">
                                <i class="bi bi-x-circle me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Real-time capacity calculation
document.addEventListener('DOMContentLoaded', function() {
    const maxCapacityInput = document.getElementById('{{ form.max_capacity.id_for_label }}');
    const currentCapacityInput = document.getElementById('{{ form.current_capacity.id_for_label }}');
    const form = document.querySelector('form');
    const submitBtn = document.querySelector('button[type="submit"]');

    function updateCapacityIndicator() {
        const maxCapacity = parseFloat(maxCapacityInput.value) || 0;
        const currentCapacity = parseFloat(currentCapacityInput.value) || 0;

        if (maxCapacity > 0) {
            const percentage = (currentCapacity / maxCapacity) * 100;
            console.log('Capacity percentage:', percentage.toFixed(1) + '%');
        }
    }

    if (maxCapacityInput && currentCapacityInput) {
        maxCapacityInput.addEventListener('input', updateCapacityIndicator);
        currentCapacityInput.addEventListener('input', updateCapacityIndicator);
    }

    // تتبع إرسال النموذج - مبسط
    if (form && submitBtn) {
        submitBtn.addEventListener('click', function(e) {
            console.log('DEBUG: Submit button clicked');

            // تغيير نص الزر
            this.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري المعالجة...';
            this.disabled = true;

            console.log('DEBUG: Button text changed, form will submit');

            // السماح للنموذج بالإرسال العادي
            setTimeout(() => {
                form.submit();
            }, 100);
        });
    }
});
</script>
{% endblock %}
