from django.db import models
from django.contrib.auth.models import User
from django.core.validators import RegexValidator, MinValueValidator
from decimal import Decimal

class Bank(models.Model):
    """نموذج البنوك"""
    name = models.Char<PERSON>ield(max_length=200, verbose_name="اسم البنك")
    code = models.CharField(max_length=10, unique=True, verbose_name="كود البنك")
    swift_code = models.CharField(max_length=11, blank=True, null=True, verbose_name="كود SWIFT")
    address = models.TextField(blank=True, null=True, verbose_name="العنوان")
    phone = models.CharField(max_length=20, blank=True, null=True, verbose_name="رقم الهاتف")
    email = models.EmailField(blank=True, null=True, verbose_name="البريد الإلكتروني")
    website = models.URLField(blank=True, null=True, verbose_name="الموقع الإلكتروني")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "بنك"
        verbose_name_plural = "البنوك"
        ordering = ['name']

    def __str__(self):
        return self.name

class BankAccount(models.Model):
    """نموذج الحسابات البنكية"""
    ACCOUNT_TYPE_CHOICES = [
        ('checking', 'حساب جاري'),
        ('savings', 'حساب توفير'),
        ('fixed_deposit', 'وديعة ثابتة'),
        ('credit', 'حساب ائتماني'),
    ]

    CURRENCY_CHOICES = [
        ('SAR', 'ريال سعودي'),
        ('USD', 'دولار أمريكي'),
        ('EUR', 'يورو'),
        ('GBP', 'جنيه إسترليني'),
    ]

    bank = models.ForeignKey(Bank, on_delete=models.CASCADE, verbose_name="البنك")
    account_name = models.CharField(max_length=200, verbose_name="اسم الحساب")
    account_number = models.CharField(max_length=50, unique=True, verbose_name="رقم الحساب")
    iban = models.CharField(max_length=34, blank=True, null=True, verbose_name="رقم IBAN")
    account_type = models.CharField(max_length=20, choices=ACCOUNT_TYPE_CHOICES, verbose_name="نوع الحساب")
    currency = models.CharField(max_length=3, choices=CURRENCY_CHOICES, default='SAR', verbose_name="العملة")
    opening_balance = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="الرصيد الافتتاحي")
    current_balance = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="الرصيد الحالي")
    minimum_balance = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="الحد الأدنى للرصيد")
    overdraft_limit = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="حد السحب على المكشوف")
    interest_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="معدل الفائدة (%)")
    account_manager = models.CharField(max_length=200, blank=True, null=True, verbose_name="مدير الحساب")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "حساب بنكي"
        verbose_name_plural = "الحسابات البنكية"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.account_name} - {self.bank.name}"

    @property
    def available_balance(self):
        """الرصيد المتاح للسحب"""
        return self.current_balance + self.overdraft_limit

    @property
    def is_overdrawn(self):
        """هل الحساب مسحوب على المكشوف"""
        return self.current_balance < 0

class BankTransaction(models.Model):
    """نموذج المعاملات البنكية"""
    TRANSACTION_TYPE_CHOICES = [
        ('deposit', 'إيداع'),
        ('withdrawal', 'سحب'),
        ('transfer_in', 'تحويل وارد'),
        ('transfer_out', 'تحويل صادر'),
        ('fee', 'رسوم'),
        ('interest', 'فوائد'),
        ('adjustment', 'تسوية'),
    ]

    STATUS_CHOICES = [
        ('pending', 'معلق'),
        ('completed', 'مكتمل'),
        ('cancelled', 'ملغي'),
        ('failed', 'فاشل'),
    ]

    account = models.ForeignKey(BankAccount, related_name='transactions', on_delete=models.CASCADE, verbose_name="الحساب")
    transaction_date = models.DateField(verbose_name="تاريخ المعاملة")
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPE_CHOICES, verbose_name="نوع المعاملة")
    amount = models.DecimalField(max_digits=15, decimal_places=2, validators=[MinValueValidator(0.01)], verbose_name="المبلغ")
    description = models.TextField(verbose_name="وصف المعاملة")
    reference_number = models.CharField(max_length=100, blank=True, null=True, verbose_name="رقم المرجع")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name="الحالة")
    balance_before = models.DecimalField(max_digits=15, decimal_places=2, verbose_name="الرصيد قبل المعاملة")
    balance_after = models.DecimalField(max_digits=15, decimal_places=2, verbose_name="الرصيد بعد المعاملة")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "معاملة بنكية"
        verbose_name_plural = "المعاملات البنكية"
        ordering = ['-transaction_date', '-created_at']

    def __str__(self):
        return f"{self.get_transaction_type_display()} - {self.amount} - {self.account.account_name}"

    def save(self, *args, **kwargs):
        """تحديث رصيد الحساب عند حفظ المعاملة"""
        if not self.pk:  # معاملة جديدة
            self.balance_before = self.account.current_balance

            if self.transaction_type in ['deposit', 'transfer_in', 'interest']:
                self.balance_after = self.balance_before + self.amount
            elif self.transaction_type in ['withdrawal', 'transfer_out', 'fee']:
                self.balance_after = self.balance_before - self.amount
            else:  # adjustment
                self.balance_after = self.balance_before + self.amount

            # تحديث رصيد الحساب
            self.account.current_balance = self.balance_after
            self.account.save()

        super().save(*args, **kwargs)
