<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نموذج الشخص</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: #ffffff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-label {
            color: #333;
            font-weight: 600;
        }
        .form-control {
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .btn-primary {
            background: #007bff;
            border: none;
            padding: 10px 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2 class="text-center mb-4">اختبار نموذج إضافة شخص</h2>
        
        <form action="http://127.0.0.1:8000/definitions/persons/create/" method="post">
            <input type="hidden" name="csrfmiddlewaretoken" value="test">
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="code" class="form-label">كود الشخص *</label>
                    <input type="text" class="form-control" id="code" name="code" value="TEST001" required>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="name" class="form-label">الاسم *</label>
                    <input type="text" class="form-control" id="name" name="name" value="شخص تجريبي" required>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="person_type" class="form-label">نوع الشخص *</label>
                    <select class="form-control" id="person_type" name="person_type" required>
                        <option value="">-- اختر نوع الشخص --</option>
                        <option value="customer">عميل</option>
                        <option value="supplier">مورد</option>
                        <option value="employee">موظف</option>
                        <option value="both">عميل ومورد</option>
                        <option value="other">أخرى</option>
                    </select>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="phone" class="form-label">الهاتف</label>
                    <input type="text" class="form-control" id="phone" name="phone" value="01234567890">
                </div>
            </div>
            
            <div class="mb-3">
                <label for="email" class="form-label">البريد الإلكتروني</label>
                <input type="email" class="form-control" id="email" name="email" value="<EMAIL>">
            </div>
            
            <div class="mb-3">
                <label for="address" class="form-label">العنوان</label>
                <textarea class="form-control" id="address" name="address" rows="3">عنوان تجريبي</textarea>
            </div>
            
            <div class="mb-3">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                    <label class="form-check-label" for="is_active">
                        شخص نشط
                    </label>
                </div>
            </div>
            
            <div class="text-center">
                <button type="submit" class="btn btn-primary">إضافة الشخص</button>
            </div>
        </form>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test form loaded');
            
            const form = document.querySelector('form');
            form.addEventListener('submit', function(e) {
                console.log('Form submitted');
                
                const formData = new FormData(form);
                console.log('Form data:');
                for (let [key, value] of formData.entries()) {
                    console.log(key + ': ' + value);
                }
            });
        });
    </script>
</body>
</html>
