from django.contrib import admin
from .models import (
    WarehouseDefinition, ProductCategory, ProductDefinition,
    PersonDefinition, AssetGroup, ExpenseType, ExpenseName,
    RevenueType, RevenueName, ProfitCenter
)

@admin.register(WarehouseDefinition)
class WarehouseDefinitionAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'warehouse_type', 'is_active']
    list_filter = ['warehouse_type', 'is_active']
    search_fields = ['code', 'name']

@admin.register(ProductCategory)
class ProductCategoryAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'parent', 'is_active']
    list_filter = ['is_active']
    search_fields = ['code', 'name']

@admin.register(ProductDefinition)
class ProductDefinitionAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'category', 'product_type', 'cost_price', 'selling_price', 'is_active']
    list_filter = ['product_type', 'category', 'is_active']
    search_fields = ['code', 'name']

@admin.register(PersonDefinition)
class PersonDefinitionAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'person_type', 'phone', 'is_active']
    list_filter = ['person_type', 'is_active']
    search_fields = ['code', 'name', 'phone']

@admin.register(AssetGroup)
class AssetGroupAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'depreciation_rate', 'useful_life_years', 'is_active']
    list_filter = ['is_active']
    search_fields = ['code', 'name']

@admin.register(ExpenseType)
class ExpenseTypeAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'is_active']
    list_filter = ['is_active']
    search_fields = ['code', 'name']

@admin.register(ExpenseName)
class ExpenseNameAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'expense_type', 'is_active']
    list_filter = ['expense_type', 'is_active']
    search_fields = ['code', 'name']

@admin.register(RevenueType)
class RevenueTypeAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'is_active']
    list_filter = ['is_active']
    search_fields = ['code', 'name']

@admin.register(RevenueName)
class RevenueNameAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'revenue_type', 'is_active']
    list_filter = ['revenue_type', 'is_active']
    search_fields = ['code', 'name']

@admin.register(ProfitCenter)
class ProfitCenterAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'is_active']
    list_filter = ['is_active']
    search_fields = ['code', 'name']
