{% extends 'base.html' %}

{% block title %}تفاصيل العملة - {{ currency.name }}{% endblock %}

{% block extra_css %}
<style>
    .currency-detail-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        padding: 2rem 0;
        margin-bottom: 2rem;
        color: white;
        border-radius: 15px;
    }

    .detail-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .detail-section {
        padding: 2rem;
        border-bottom: 1px solid #eee;
    }

    .detail-section:last-child {
        border-bottom: none;
    }

    .section-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 1.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #28a745;
    }

    .detail-item {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 8px;
    }

    .detail-label {
        font-weight: 600;
        color: #495057;
        min-width: 150px;
        margin-bottom: 0;
    }

    .detail-value {
        color: #212529;
        flex: 1;
    }

    .detail-value strong {
        color: #28a745;
    }

    .currency-symbol {
        font-size: 1.5rem;
        font-weight: bold;
        color: #28a745;
    }

    .base-currency-badge {
        background: #ffd700;
        color: #856404;
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-weight: 600;
    }

    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-weight: 600;
    }

    .status-active {
        background: #d4edda;
        color: #155724;
    }

    .status-inactive {
        background: #f8d7da;
        color: #721c24;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        padding: 2rem;
        background: #f8f9fa;
    }
</style>
{% endblock %}

{% block content %}
<!-- Detail Header -->
<div class="currency-detail-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-currency-exchange me-2"></i>
                    {{ currency.name }}
                </h1>
                <p class="mb-0 opacity-75">تفاصيل العملة - {{ currency.code }}</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'definitions:currency_list' %}" class="btn btn-light">
                    <i class="bi bi-arrow-left me-2"></i>العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Detail Content -->
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="detail-card">
                <!-- Basic Information -->
                <div class="detail-section">
                    <h4 class="section-title">
                        <i class="bi bi-info-circle me-2"></i>المعلومات الأساسية
                    </h4>
                    
                    <div class="detail-item">
                        <div class="detail-label">كود العملة:</div>
                        <div class="detail-value">
                            <strong>{{ currency.code }}</strong>
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">اسم العملة:</div>
                        <div class="detail-value">
                            <strong>{{ currency.name }}</strong>
                        </div>
                    </div>

                    {% if currency.name_en %}
                    <div class="detail-item">
                        <div class="detail-label">الاسم بالإنجليزية:</div>
                        <div class="detail-value">{{ currency.name_en }}</div>
                    </div>
                    {% endif %}

                    <div class="detail-item">
                        <div class="detail-label">رمز العملة:</div>
                        <div class="detail-value">
                            <span class="currency-symbol">{{ currency.symbol }}</span>
                        </div>
                    </div>
                </div>

                <!-- Exchange Rate Information -->
                <div class="detail-section">
                    <h4 class="section-title">
                        <i class="bi bi-graph-up me-2"></i>معلومات سعر الصرف
                    </h4>
                    
                    <div class="detail-item">
                        <div class="detail-label">سعر الصرف:</div>
                        <div class="detail-value">
                            {% if currency.is_base_currency %}
                                <strong>1.00 (العملة الأساسية)</strong>
                            {% else %}
                                <strong>{{ currency.exchange_rate|floatformat:2 }}</strong>
                            {% endif %}
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">نوع العملة:</div>
                        <div class="detail-value">
                            {% if currency.is_base_currency %}
                                <span class="base-currency-badge">
                                    <i class="bi bi-star-fill me-1"></i>عملة أساسية
                                </span>
                            {% else %}
                                <span class="text-muted">عملة فرعية</span>
                            {% endif %}
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">حالة العملة:</div>
                        <div class="detail-value">
                            <span class="status-badge {% if currency.is_active %}status-active{% else %}status-inactive{% endif %}">
                                <i class="bi bi-{% if currency.is_active %}check-circle{% else %}x-circle{% endif %} me-1"></i>
                                {{ currency.is_active|yesno:"نشط,غير نشط" }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- System Information -->
                <div class="detail-section">
                    <h4 class="section-title">
                        <i class="bi bi-gear me-2"></i>معلومات النظام
                    </h4>
                    
                    <div class="detail-item">
                        <div class="detail-label">تاريخ الإنشاء:</div>
                        <div class="detail-value">{{ currency.created_at|date:"d/m/Y H:i" }}</div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">آخر تحديث:</div>
                        <div class="detail-value">{{ currency.updated_at|date:"d/m/Y H:i" }}</div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">أنشئ بواسطة:</div>
                        <div class="detail-value">{{ currency.created_by.get_full_name|default:currency.created_by.username }}</div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons">
                    <a href="{% url 'definitions:currency_edit' currency.id %}" class="btn btn-warning btn-lg">
                        <i class="bi bi-pencil me-2"></i>تعديل العملة
                    </a>
                    {% if not currency.is_base_currency %}
                    <a href="{% url 'definitions:currency_delete' currency.id %}" class="btn btn-danger btn-lg">
                        <i class="bi bi-trash me-2"></i>حذف العملة
                    </a>
                    {% else %}
                    <button class="btn btn-secondary btn-lg" disabled title="لا يمكن حذف العملة الأساسية">
                        <i class="bi bi-lock me-2"></i>محمية من الحذف
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
