{% extends 'base_form.html' %}

{% block title %}
{% if action == 'edit' %}
تعديل مجموعة الأصول - {{ asset_group.name }}
{% else %}
إضافة مجموعة أصول جديدة
{% endif %}
{% endblock %}

{% block form_icon %}
<i class="bi bi-collection"></i>
{% endblock %}

{% block form_title %}
{% if action == 'edit' %}
تعديل مجموعة الأصول
{% else %}
إضافة مجموعة أصول جديدة
{% endif %}
{% endblock %}

{% block back_url %}{% url 'definitions:asset_group_list' %}{% endblock %}

{% block submit_text %}
{% if action == 'edit' %}
حفظ التعديلات
{% else %}
إضافة مجموعة الأصول
{% endif %}
{% endblock %}

{% block form_content %}
<!-- Basic Information Section -->
<div class="form-section">
    <h3 class="section-title">
        <i class="bi bi-info-circle"></i>
        المعلومات الأساسية
    </h3>
    
    <div class="row">
        <div class="col-md-4 mb-3">
            <label for="code" class="form-label required">
                <i class="bi bi-hash"></i>كود مجموعة الأصول
            </label>
            <input type="text" 
                   class="form-control" 
                   id="code" 
                   name="code" 
                   value="{% if action == 'edit' %}{{ asset_group.code }}{% else %}{{ form_data.code|default:'' }}{% endif %}"
                   placeholder="مثال: COMP، FURN، VEHI"
                   maxlength="10"
                   style="text-transform: uppercase;"
                   required>
            <div class="form-text">كود مختصر لمجموعة الأصول</div>
        </div>

        <div class="col-md-4 mb-3">
            <label for="name" class="form-label required">
                <i class="bi bi-collection"></i>اسم مجموعة الأصول
            </label>
            <input type="text" 
                   class="form-control" 
                   id="name" 
                   name="name" 
                   value="{% if action == 'edit' %}{{ asset_group.name }}{% else %}{{ form_data.name|default:'' }}{% endif %}"
                   placeholder="الاسم بالعربية"
                   required>
            <div class="form-text">اسم مجموعة الأصول بالعربية</div>
        </div>

        <div class="col-md-4 mb-3">
            <label for="name_en" class="form-label">
                <i class="bi bi-globe"></i>الاسم بالإنجليزية
            </label>
            <input type="text" 
                   class="form-control" 
                   id="name_en" 
                   name="name_en" 
                   value="{% if action == 'edit' %}{{ asset_group.name_en }}{% else %}{{ form_data.name_en|default:'' }}{% endif %}"
                   placeholder="Name in English">
            <div class="form-text">اسم مجموعة الأصول بالإنجليزية (اختياري)</div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6 mb-3">
            <label for="parent_group" class="form-label">
                <i class="bi bi-diagram-3"></i>المجموعة الأب
            </label>
            <select class="form-select" id="parent_group" name="parent_group">
                <option value="">-- اختر المجموعة الأب --</option>
                {% for parent in parent_groups %}
                <option value="{{ parent.id }}" 
                        {% if action == 'edit' and asset_group.parent_group and asset_group.parent_group.id == parent.id %}selected
                        {% elif form_data.parent_group == parent.id|stringformat:"s" %}selected{% endif %}>
                    {{ parent.name }} ({{ parent.code }})
                </option>
                {% endfor %}
            </select>
            <div class="form-text">المجموعة الرئيسية (اختياري)</div>
        </div>

        <div class="col-md-6 mb-3">
            <label for="asset_type" class="form-label required">
                <i class="bi bi-tags"></i>نوع الأصول
            </label>
            <select class="form-select" id="asset_type" name="asset_type" required>
                <option value="">-- اختر نوع الأصول --</option>
                <option value="fixed" {% if action == 'edit' and asset_group.asset_type == 'fixed' %}selected{% elif form_data.asset_type == 'fixed' %}selected{% endif %}>أصول ثابتة</option>
                <option value="current" {% if action == 'edit' and asset_group.asset_type == 'current' %}selected{% elif form_data.asset_type == 'current' %}selected{% endif %}>أصول متداولة</option>
                <option value="intangible" {% if action == 'edit' and asset_group.asset_type == 'intangible' %}selected{% elif form_data.asset_type == 'intangible' %}selected{% endif %}>أصول غير ملموسة</option>
                <option value="investment" {% if action == 'edit' and asset_group.asset_type == 'investment' %}selected{% elif form_data.asset_type == 'investment' %}selected{% endif %}>استثمارات</option>
                <option value="other" {% if action == 'edit' and asset_group.asset_type == 'other' %}selected{% elif form_data.asset_type == 'other' %}selected{% endif %}>أخرى</option>
            </select>
            <div class="form-text">تصنيف نوع الأصول</div>
        </div>
    </div>
</div>

<!-- Depreciation Settings Section -->
<div class="form-section">
    <h3 class="section-title">
        <i class="bi bi-graph-down"></i>
        إعدادات الإهلاك
    </h3>
    
    <div class="row">
        <div class="col-md-6 mb-3">
            <label for="depreciation_method" class="form-label">
                <i class="bi bi-calculator"></i>طريقة الإهلاك
            </label>
            <select class="form-select" id="depreciation_method" name="depreciation_method">
                <option value="">-- اختر طريقة الإهلاك --</option>
                <option value="straight_line" {% if action == 'edit' and asset_group.depreciation_method == 'straight_line' %}selected{% elif form_data.depreciation_method == 'straight_line' %}selected{% endif %}>القسط الثابت</option>
                <option value="declining_balance" {% if action == 'edit' and asset_group.depreciation_method == 'declining_balance' %}selected{% elif form_data.depreciation_method == 'declining_balance' %}selected{% endif %}>الرصيد المتناقص</option>
                <option value="units_production" {% if action == 'edit' and asset_group.depreciation_method == 'units_production' %}selected{% elif form_data.depreciation_method == 'units_production' %}selected{% endif %}>وحدات الإنتاج</option>
                <option value="sum_years" {% if action == 'edit' and asset_group.depreciation_method == 'sum_years' %}selected{% elif form_data.depreciation_method == 'sum_years' %}selected{% endif %}>مجموع السنوات</option>
                <option value="no_depreciation" {% if action == 'edit' and asset_group.depreciation_method == 'no_depreciation' %}selected{% elif form_data.depreciation_method == 'no_depreciation' %}selected{% endif %}>بدون إهلاك</option>
            </select>
            <div class="form-text">طريقة حساب الإهلاك</div>
        </div>

        <div class="col-md-6 mb-3">
            <label for="useful_life_years" class="form-label">
                <i class="bi bi-calendar-range"></i>العمر الافتراضي (سنوات)
            </label>
            <input type="number" 
                   class="form-control" 
                   id="useful_life_years" 
                   name="useful_life_years" 
                   min="1"
                   max="100"
                   value="{% if action == 'edit' %}{{ asset_group.useful_life_years }}{% else %}{{ form_data.useful_life_years|default:'' }}{% endif %}"
                   placeholder="عدد السنوات">
            <div class="form-text">العمر الافتراضي بالسنوات</div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6 mb-3">
            <label for="depreciation_rate" class="form-label">
                <i class="bi bi-percent"></i>معدل الإهلاك (%)
            </label>
            <input type="number" 
                   class="form-control" 
                   id="depreciation_rate" 
                   name="depreciation_rate" 
                   step="0.01"
                   min="0"
                   max="100"
                   value="{% if action == 'edit' %}{{ asset_group.depreciation_rate }}{% else %}{{ form_data.depreciation_rate|default:'' }}{% endif %}"
                   placeholder="0.00">
            <div class="form-text">معدل الإهلاك السنوي (%)</div>
        </div>

        <div class="col-md-6 mb-3">
            <label for="salvage_value_rate" class="form-label">
                <i class="bi bi-piggy-bank"></i>نسبة القيمة المتبقية (%)
            </label>
            <input type="number" 
                   class="form-control" 
                   id="salvage_value_rate" 
                   name="salvage_value_rate" 
                   step="0.01"
                   min="0"
                   max="100"
                   value="{% if action == 'edit' %}{{ asset_group.salvage_value_rate }}{% else %}{{ form_data.salvage_value_rate|default:'0' }}{% endif %}"
                   placeholder="0.00">
            <div class="form-text">نسبة القيمة المتبقية في نهاية العمر الافتراضي</div>
        </div>
    </div>
</div>

<!-- Accounting Settings Section -->
<div class="form-section">
    <h3 class="section-title">
        <i class="bi bi-journal-bookmark"></i>
        الإعدادات المحاسبية
    </h3>
    
    <div class="row">
        <div class="col-md-6 mb-3">
            <label for="asset_account" class="form-label">
                <i class="bi bi-wallet2"></i>حساب الأصول
            </label>
            <select class="form-select" id="asset_account" name="asset_account">
                <option value="">-- اختر حساب الأصول --</option>
                {% for account in asset_accounts %}
                <option value="{{ account.id }}" 
                        {% if action == 'edit' and asset_group.asset_account and asset_group.asset_account.id == account.id %}selected
                        {% elif form_data.asset_account == account.id|stringformat:"s" %}selected{% endif %}>
                    {{ account.name }} ({{ account.code }})
                </option>
                {% endfor %}
            </select>
            <div class="form-text">الحساب المحاسبي للأصول</div>
        </div>

        <div class="col-md-6 mb-3">
            <label for="depreciation_account" class="form-label">
                <i class="bi bi-graph-down-arrow"></i>حساب مجمع الإهلاك
            </label>
            <select class="form-select" id="depreciation_account" name="depreciation_account">
                <option value="">-- اختر حساب مجمع الإهلاك --</option>
                {% for account in depreciation_accounts %}
                <option value="{{ account.id }}" 
                        {% if action == 'edit' and asset_group.depreciation_account and asset_group.depreciation_account.id == account.id %}selected
                        {% elif form_data.depreciation_account == account.id|stringformat:"s" %}selected{% endif %}>
                    {{ account.name }} ({{ account.code }})
                </option>
                {% endfor %}
            </select>
            <div class="form-text">حساب مجمع الإهلاك</div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6 mb-3">
            <label for="expense_account" class="form-label">
                <i class="bi bi-receipt"></i>حساب مصروف الإهلاك
            </label>
            <select class="form-select" id="expense_account" name="expense_account">
                <option value="">-- اختر حساب مصروف الإهلاك --</option>
                {% for account in expense_accounts %}
                <option value="{{ account.id }}" 
                        {% if action == 'edit' and asset_group.expense_account and asset_group.expense_account.id == account.id %}selected
                        {% elif form_data.expense_account == account.id|stringformat:"s" %}selected{% endif %}>
                    {{ account.name }} ({{ account.code }})
                </option>
                {% endfor %}
            </select>
            <div class="form-text">حساب مصروف الإهلاك</div>
        </div>
    </div>
</div>

<!-- Additional Information Section -->
<div class="form-section">
    <h3 class="section-title">
        <i class="bi bi-gear"></i>
        معلومات إضافية
    </h3>
    
    <div class="row">
        <div class="col-md-12 mb-3">
            <label for="description" class="form-label">
                <i class="bi bi-file-text"></i>وصف مجموعة الأصول
            </label>
            <textarea class="form-control" 
                      id="description" 
                      name="description" 
                      rows="3"
                      placeholder="وصف تفصيلي لمجموعة الأصول...">{% if action == 'edit' %}{{ asset_group.description }}{% else %}{{ form_data.description|default:'' }}{% endif %}</textarea>
            <div class="form-text">وصف تفصيلي لمجموعة الأصول (اختياري)</div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6 mb-3">
            <div class="form-check">
                <input class="form-check-input" 
                       type="checkbox" 
                       id="requires_maintenance" 
                       name="requires_maintenance"
                       {% if action == 'edit' and asset_group.requires_maintenance %}checked
                       {% elif form_data.requires_maintenance %}checked{% endif %}>
                <label class="form-check-label" for="requires_maintenance">
                    <i class="bi bi-tools me-1"></i>تتطلب صيانة
                </label>
                <div class="form-text">هل تتطلب أصول هذه المجموعة صيانة دورية</div>
            </div>
        </div>

        <div class="col-md-6 mb-3">
            <div class="form-check">
                <input class="form-check-input" 
                       type="checkbox" 
                       id="is_active" 
                       name="is_active"
                       {% if action == 'edit' and asset_group.is_active %}checked
                       {% elif action != 'edit' %}checked{% endif %}>
                <label class="form-check-label" for="is_active">
                    <i class="bi bi-toggle-on me-1"></i>مجموعة نشطة
                </label>
                <div class="form-text">تفعيل أو إلغاء تفعيل مجموعة الأصول</div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12 mb-3">
            <label for="notes" class="form-label">
                <i class="bi bi-journal-text"></i>ملاحظات
            </label>
            <textarea class="form-control" 
                      id="notes" 
                      name="notes" 
                      rows="3"
                      placeholder="ملاحظات إضافية...">{% if action == 'edit' %}{{ asset_group.notes }}{% else %}{{ form_data.notes|default:'' }}{% endif %}</textarea>
            <div class="form-text">ملاحظات إضافية (اختياري)</div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحويل الكود إلى أحرف كبيرة تلقائياً
    const codeInput = document.getElementById('code');
    if (codeInput) {
        codeInput.addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });
    }
    
    // حساب معدل الإهلاك تلقائياً عند تغيير العمر الافتراضي
    const usefulLifeInput = document.getElementById('useful_life_years');
    const depreciationRateInput = document.getElementById('depreciation_rate');
    const depreciationMethodSelect = document.getElementById('depreciation_method');
    
    function calculateDepreciationRate() {
        const usefulLife = parseFloat(usefulLifeInput.value);
        const method = depreciationMethodSelect.value;
        
        if (usefulLife > 0 && method === 'straight_line') {
            const rate = (100 / usefulLife).toFixed(2);
            depreciationRateInput.value = rate;
        }
    }
    
    if (usefulLifeInput && depreciationRateInput && depreciationMethodSelect) {
        usefulLifeInput.addEventListener('input', calculateDepreciationRate);
        depreciationMethodSelect.addEventListener('change', function() {
            if (this.value === 'no_depreciation') {
                depreciationRateInput.value = '0';
                depreciationRateInput.readOnly = true;
                usefulLifeInput.readOnly = true;
                depreciationRateInput.style.backgroundColor = '#f8f9fa';
                usefulLifeInput.style.backgroundColor = '#f8f9fa';
            } else {
                depreciationRateInput.readOnly = false;
                usefulLifeInput.readOnly = false;
                depreciationRateInput.style.backgroundColor = '';
                usefulLifeInput.style.backgroundColor = '';
                calculateDepreciationRate();
            }
        });
        
        // تطبيق الحالة الأولية
        if (depreciationMethodSelect.value === 'no_depreciation') {
            depreciationRateInput.readOnly = true;
            usefulLifeInput.readOnly = true;
            depreciationRateInput.style.backgroundColor = '#f8f9fa';
            usefulLifeInput.style.backgroundColor = '#f8f9fa';
        }
    }
    
    // اقتراحات بناءً على نوع الأصول
    const assetTypeSelect = document.getElementById('asset_type');
    if (assetTypeSelect) {
        assetTypeSelect.addEventListener('change', function() {
            const suggestions = {
                'fixed': { life: 10, rate: 10.00 },
                'current': { life: 1, rate: 100.00 },
                'intangible': { life: 5, rate: 20.00 },
                'investment': { life: '', rate: 0 }
            };
            
            const selectedType = this.value;
            if (suggestions[selectedType] && !usefulLifeInput.value) {
                if (suggestions[selectedType].life) {
                    usefulLifeInput.value = suggestions[selectedType].life;
                    depreciationRateInput.value = suggestions[selectedType].rate;
                }
            }
        });
    }
});
</script>
{% endblock %}
