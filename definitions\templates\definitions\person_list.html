{% extends 'base.html' %}
{% load static %}

{% block title %}إدارة تعريف الأشخاص{% endblock %}

{% block extra_css %}
<style>
    /* Advanced Persons Management Styles */
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        --warning-gradient: linear-gradient(135deg, #f7971e 0%, #ffd200 100%);
        --danger-gradient: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
        --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --purple-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --teal-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --orange-gradient: linear-gradient(135deg, #ff9a56 0%, #ff6b95 100%);
        --pink-gradient: linear-gradient(135deg, #ff6b95 0%, #ff9a56 100%);
        --glass-bg: rgba(255, 255, 255, 0.15);
        --glass-border: rgba(255, 255, 255, 0.2);
        --glass-backdrop: blur(15px);
        --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
        --radius: 12px;
        --radius-lg: 20px;
    }

    body {
        background: #ffffff;
        min-height: 100vh;
    }

    /* Page Header */
    .page-header {
        background: #ffffff;
        border: 1px solid #e0e0e0;
        border-radius: var(--radius-lg);
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        padding: 2rem;
        margin-bottom: 2rem;
        color: #333;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        color: #333;
    }

    .page-subtitle {
        font-size: 1.1rem;
        color: #666;
        margin: 0.5rem 0 0 0;
    }

    /* Filters */
    .filters-section {
        background: #ffffff;
        border: 1px solid #e0e0e0;
        padding: 1.5rem;
        border-radius: var(--radius-lg);
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 1.5rem;
    }

    .filters-title {
        color: #333;
        font-size: 1.2rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }

    .filters-row {
        display: grid;
        grid-template-columns: 2fr 1fr auto;
        gap: 1rem;
        align-items: end;
    }

    .form-group {
        display: flex;
        flex-direction: column;
    }

    .form-label {
        color: #333;
        font-weight: 600;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
    }

    .form-control {
        padding: 0.75rem 1rem;
        background: #ffffff;
        border: 1px solid #ddd;
        border-radius: var(--radius);
        font-size: 0.875rem;
        color: #333;
        transition: all 0.3s ease;
    }

    .form-control::placeholder {
        color: #999;
    }

    .form-control:focus {
        background: #ffffff;
        border-color: #007bff;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        color: #333;
        outline: none;
    }

    .form-control option {
        background: #ffffff;
        color: #333;
    }

    /* Enhanced Buttons */
    .btn {
        padding: 0.75rem 1.5rem;
        border-radius: var(--radius);
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        font-size: 0.875rem;
    }

    .btn-primary {
        background: var(--primary-gradient);
        color: white;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        color: white;
    }

    .btn-outline-light {
        background: rgba(255, 255, 255, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        backdrop-filter: blur(8px);
    }

    .btn-outline-light:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translateY(-2px);
        color: white;
        text-decoration: none;
    }

    .btn-success {
        background: var(--success-gradient);
        color: white;
        box-shadow: 0 4px 15px rgba(86, 171, 47, 0.4);
    }

    .btn-success:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(86, 171, 47, 0.6);
        color: white;
        text-decoration: none;
    }

    /* Enhanced Table */
    .table-container {
        background: #ffffff;
        border: 1px solid #e0e0e0;
        border-radius: var(--radius-lg);
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .table-header {
        padding: 1.5rem;
        border-bottom: 1px solid #e0e0e0;
        background: #f8f9fa;
    }

    .table-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: #333;
        margin: 0;
    }

    .table {
        width: 100%;
        margin: 0;
        color: #333;
    }

    .table th {
        background: #f8f9fa;
        padding: 1rem 0.75rem;
        text-align: center;
        font-weight: 700;
        color: #333;
        border-bottom: 2px solid #e0e0e0;
        font-size: 0.875rem;
    }

    .table td {
        padding: 1rem 0.75rem;
        border-bottom: 1px solid #f0f0f0;
        color: #333;
        background: #ffffff;
        text-align: center;
        font-size: 0.875rem;
    }

    .table tbody tr:hover {
        background: #f8f9fa;
        transition: all 0.3s ease;
    }

    /* Enhanced Badges */
    .badge {
        display: inline-flex;
        align-items: center;
        padding: 0.4rem 0.8rem;
        border-radius: 25px;
        font-size: 0.75rem;
        font-weight: 600;
        backdrop-filter: blur(8px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        text-shadow: 0 1px 2px rgba(0,0,0,0.4);
        transition: all 0.3s ease;
        white-space: nowrap;
    }

    .badge:hover {
        transform: translateY(-1px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.3);
    }

    .badge-customer { background: var(--primary-gradient); color: white; }
    .badge-supplier { background: var(--success-gradient); color: white; }
    .badge-employee { background: var(--warning-gradient); color: white; }
    .badge-both { background: var(--info-gradient); color: white; }
    .badge-other { background: var(--purple-gradient); color: white; }

    .badge-active {
        background: var(--success-gradient);
        color: white;
        box-shadow: 0 4px 15px rgba(86, 171, 47, 0.4);
    }

    .badge-inactive {
        background: var(--danger-gradient);
        color: white;
        box-shadow: 0 4px 15px rgba(255, 65, 108, 0.4);
    }

    .badge-male {
        background: var(--info-gradient);
        color: white;
    }

    .badge-female {
        background: var(--pink-gradient);
        color: white;
    }

    /* Action Buttons */
    .action-buttons {
        display: flex;
        gap: 0.5rem;
        justify-content: center;
    }

    .action-buttons .btn-sm {
        padding: 0.5rem;
        border-radius: 8px;
        backdrop-filter: blur(4px);
        transition: all 0.3s ease;
        font-size: 0.75rem;
    }

    .action-buttons .btn-sm:hover {
        transform: scale(1.1);
    }

    /* Contact Info */
    .contact-info {
        display: flex;
        flex-direction: column;
        gap: 0.2rem;
        font-size: 0.8rem;
    }

    .contact-info .contact-item {
        display: flex;
        align-items: center;
        gap: 0.3rem;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .filters-row {
            grid-template-columns: 1fr;
        }
        
        .page-title {
            font-size: 2rem;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        .action-buttons {
            flex-direction: column;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="page-title">
                    <i class="bi bi-people me-3"></i>
                    إدارة تعريف الأشخاص
                </h1>
                <p class="page-subtitle">
                    إدارة العملاء والموردين والموظفين وجميع الأشخاص في النظام
                </p>
            </div>
            <div class="d-flex gap-2">
                <a href="{% url 'definitions:dashboard' %}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-left me-2"></i>
                    عودة للتعريفات
                </a>
                <a href="{% url 'definitions:person_create' %}" class="btn btn-success">
                    <i class="bi bi-person-plus me-2"></i>
                    إضافة شخص جديد
                </a>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filters-section">
        <h3 class="filters-title">
            <i class="bi bi-funnel me-2"></i>
            البحث والفلترة
        </h3>
        <form method="get" class="filters-form">
            <div class="filters-row">
                <div class="form-group">
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control"
                           placeholder="البحث بالاسم، الكود، الهاتف، أو البريد الإلكتروني..."
                           value="{{ search_query }}">
                </div>
                <div class="form-group">
                    <label class="form-label">نوع الشخص</label>
                    <select name="person_type" class="form-control">
                        <option value="">جميع الأنواع</option>
                        {% for type_code, type_name in person_types %}
                        <option value="{{ type_code }}" {% if person_type == type_code %}selected{% endif %}>
                            {{ type_name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search"></i>
                        بحث
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Persons Table -->
    <div class="table-container">
        <div class="table-header">
            <h3 class="table-title">
                <i class="bi bi-table me-2"></i>
                قائمة الأشخاص
                {% if search_query or person_type %}
                <small class="ms-2">({{ page_obj.paginator.count }} نتيجة)</small>
                {% endif %}
            </h3>
        </div>

        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>الكود</th>
                        <th>الاسم</th>
                        <th>نوع الشخص</th>
                        <th>الجنس</th>
                        <th>معلومات الاتصال</th>
                        <th>المدينة</th>
                        <th>حد الائتمان</th>
                        <th>الحالة</th>
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for person in page_obj %}
                    <tr>
                        <td>
                            <strong>{{ person.code }}</strong>
                        </td>
                        <td>
                            <div>
                                <strong>{{ person.name }}</strong>
                                {% if person.name_en %}
                                <br><small class="text-muted">{{ person.name_en }}</small>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <span class="badge badge-{{ person.person_type }}">
                                {% if person.person_type == 'customer' %}
                                <i class="bi bi-person-check me-1"></i>
                                {% elif person.person_type == 'supplier' %}
                                <i class="bi bi-truck me-1"></i>
                                {% elif person.person_type == 'employee' %}
                                <i class="bi bi-person-badge me-1"></i>
                                {% elif person.person_type == 'both' %}
                                <i class="bi bi-people me-1"></i>
                                {% else %}
                                <i class="bi bi-person me-1"></i>
                                {% endif %}
                                {{ person.get_person_type_display }}
                            </span>
                        </td>
                        <td>
                            {% if person.gender %}
                            <span class="badge badge-{{ person.gender }}">
                                {% if person.gender == 'male' %}
                                <i class="bi bi-gender-male me-1"></i>ذكر
                                {% else %}
                                <i class="bi bi-gender-female me-1"></i>أنثى
                                {% endif %}
                            </span>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="contact-info">
                                {% if person.phone %}
                                <div class="contact-item">
                                    <i class="bi bi-telephone"></i>
                                    <span>{{ person.phone }}</span>
                                </div>
                                {% endif %}
                                {% if person.mobile %}
                                <div class="contact-item">
                                    <i class="bi bi-phone"></i>
                                    <span>{{ person.mobile }}</span>
                                </div>
                                {% endif %}
                                {% if person.email %}
                                <div class="contact-item">
                                    <i class="bi bi-envelope"></i>
                                    <span>{{ person.email }}</span>
                                </div>
                                {% endif %}
                                {% if not person.phone and not person.mobile and not person.email %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            {% if person.city %}
                            {{ person.city }}
                            {% if person.state %}
                            <br><small class="text-muted">{{ person.state }}</small>
                            {% endif %}
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if person.credit_limit > 0 %}
                            <strong>{{ person.credit_limit|floatformat:2 }}</strong>
                            {% if person.currency %}
                            <br><small class="text-muted">{{ person.currency.code }}</small>
                            {% endif %}
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if person.is_active %}
                            <span class="badge badge-active">
                                <i class="bi bi-check-circle me-1"></i>نشط
                            </span>
                            {% else %}
                            <span class="badge badge-inactive">
                                <i class="bi bi-x-circle me-1"></i>غير نشط
                            </span>
                            {% endif %}
                        </td>
                        <td>
                            <div>
                                {{ person.created_at|date:"d/m/Y" }}
                                <br><small class="text-muted">{{ person.created_at|date:"H:i" }}</small>
                            </div>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <a href="{% url 'definitions:person_detail' person.id %}"
                                   class="btn btn-outline-light btn-sm" title="عرض">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{% url 'definitions:person_edit' person.id %}"
                                   class="btn btn-outline-light btn-sm" title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <a href="{% url 'definitions:person_delete' person.id %}"
                                   class="btn btn-outline-light btn-sm text-danger" title="حذف"
                                   onclick="return confirm('هل أنت متأكد من حذف هذا الشخص؟')">
                                    <i class="bi bi-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="10" class="text-center py-5">
                            <div class="text-muted">
                                <i class="bi bi-people" style="font-size: 3rem;"></i>
                                <h5 class="mt-3">لا توجد أشخاص مسجلين</h5>
                                <p>ابدأ بإضافة العملاء والموردين والموظفين</p>
                                <a href="{% url 'definitions:person_create' %}" class="btn btn-success">
                                    <i class="bi bi-person-plus me-2"></i>إضافة شخص جديد
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <div class="pagination-container p-3">
            <nav>
                <ul class="pagination justify-content-center mb-0">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if person_type %}&person_type={{ person_type }}{% endif %}">
                                الأولى
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if person_type %}&person_type={{ person_type }}{% endif %}">
                                السابقة
                            </a>
                        </li>
                    {% endif %}

                    <li class="page-item active">
                        <span class="page-link">
                            {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                        </span>
                    </li>

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if person_type %}&person_type={{ person_type }}{% endif %}">
                                التالية
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if person_type %}&person_type={{ person_type }}{% endif %}">
                                الأخيرة
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
