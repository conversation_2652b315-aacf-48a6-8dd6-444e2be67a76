{% extends 'base.html' %}
{% load static %}

{% block title %}صرف مخزون - إذن إخراج{% endblock %}

{% block extra_css %}
<style>
    .remove-stock-header {
        background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        padding: 2rem 0;
        margin-bottom: 2rem;
        color: white;
        border-radius: 15px;
    }

    .form-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.5rem;
    }

    .form-control, .form-select {
        border-radius: 8px;
        border: 2px solid #e2e8f0;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }

    .btn-remove-stock {
        background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        font-weight: 600;
        color: white;
        transition: all 0.3s ease;
    }

    .btn-remove-stock:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
        color: white;
    }

    .stock-info {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 0.5rem;
    }

    .stock-available {
        color: #28a745;
        font-weight: 600;
    }

    .stock-warning {
        color: #ffc107;
        font-weight: 600;
    }

    .stock-danger {
        color: #dc3545;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<!-- Remove Stock Header -->
<div class="remove-stock-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-dash-circle me-2"></i>
                    صرف مخزون - إذن إخراج
                </h1>
                <p class="mb-0 opacity-75">صرف كميات من المخزون</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'warehouses:dashboard' %}" class="btn btn-light">
                    <i class="bi bi-arrow-right me-1"></i>العودة للمخازن
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Remove Stock Form -->
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="form-card">
                <form method="post" id="removeStockForm">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="warehouse" class="form-label">المخزن</label>
                                <select name="warehouse" id="warehouse" class="form-select" required>
                                    <option value="">اختر المخزن</option>
                                    {% for warehouse in warehouses %}
                                        <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="product" class="form-label">الصنف</label>
                                <select name="product" id="product" class="form-select" required>
                                    <option value="">اختر الصنف</option>
                                    {% for product in products %}
                                        <option value="{{ product.id }}">{{ product.name }} ({{ product.code }})</option>
                                    {% endfor %}
                                </select>
                                <div id="stockInfo" class="stock-info" style="display: none;">
                                    <small>الكمية المتوفرة: <span id="availableStock" class="stock-available">0</span></small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="quantity" class="form-label">الكمية المطلوب صرفها</label>
                                <input type="number" name="quantity" id="quantity" class="form-control" 
                                       step="0.001" min="0" required placeholder="أدخل الكمية">
                                <div id="quantityWarning" class="mt-1" style="display: none;">
                                    <small class="stock-warning">
                                        <i class="bi bi-exclamation-triangle me-1"></i>
                                        الكمية المطلوبة أكبر من المتوفر
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="reason" class="form-label">سبب الصرف</label>
                                <select name="reason" id="reason" class="form-select" required>
                                    <option value="sale">بيع</option>
                                    <option value="return_out">مرتجع صادر</option>
                                    <option value="damage">تالف</option>
                                    <option value="expired">منتهي الصلاحية</option>
                                    <option value="adjustment">تسوية جرد</option>
                                    <option value="production">استخدام في الإنتاج</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="reference_number" class="form-label">رقم المرجع (اختياري)</label>
                                <input type="text" name="reference_number" id="reference_number" class="form-control" 
                                       placeholder="رقم الفاتورة أو المرجع">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="notes" class="form-label">ملاحظات (اختياري)</label>
                        <textarea name="notes" id="notes" class="form-control" rows="3" 
                                  placeholder="أي ملاحظات إضافية..."></textarea>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-remove-stock btn-lg" id="submitBtn">
                            <i class="bi bi-dash-circle me-2"></i>
                            صرف من المخزون
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Quick Info -->
    <div class="row">
        <div class="col-12">
            <div class="alert alert-warning">
                <h6><i class="bi bi-exclamation-triangle me-2"></i>تنبيه مهم:</h6>
                <ul class="mb-0">
                    <li>تأكد من توفر الكمية المطلوبة قبل الصرف</li>
                    <li>سيتم خصم الكمية من المخزون فوراً</li>
                    <li>لا يمكن التراجع عن العملية بعد الحفظ</li>
                    <li>سيتم استخدام متوسط التكلفة للحساب</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const warehouseSelect = document.getElementById('warehouse');
    const productSelect = document.getElementById('product');
    const quantityInput = document.getElementById('quantity');
    const stockInfo = document.getElementById('stockInfo');
    const availableStock = document.getElementById('availableStock');
    const quantityWarning = document.getElementById('quantityWarning');
    const submitBtn = document.getElementById('submitBtn');
    
    let currentStock = 0;
    
    // عند تغيير المخزن أو المنتج
    function checkStock() {
        const warehouseId = warehouseSelect.value;
        const productId = productSelect.value;
        
        if (warehouseId && productId) {
            // هنا يمكن إضافة AJAX call للحصول على الرصيد الفعلي
            // مؤقتاً سنستخدم قيم وهمية
            currentStock = Math.floor(Math.random() * 100) + 10;
            availableStock.textContent = currentStock;
            stockInfo.style.display = 'block';
            
            // تحديث لون الرصيد حسب الكمية
            if (currentStock > 50) {
                availableStock.className = 'stock-available';
            } else if (currentStock > 10) {
                availableStock.className = 'stock-warning';
            } else {
                availableStock.className = 'stock-danger';
            }
        } else {
            stockInfo.style.display = 'none';
        }
    }
    
    // التحقق من الكمية المطلوبة
    function validateQuantity() {
        const requestedQuantity = parseFloat(quantityInput.value) || 0;
        
        if (requestedQuantity > currentStock) {
            quantityWarning.style.display = 'block';
            submitBtn.disabled = true;
            submitBtn.classList.add('disabled');
        } else {
            quantityWarning.style.display = 'none';
            submitBtn.disabled = false;
            submitBtn.classList.remove('disabled');
        }
    }
    
    warehouseSelect.addEventListener('change', checkStock);
    productSelect.addEventListener('change', checkStock);
    quantityInput.addEventListener('input', validateQuantity);
    
    // منع الإرسال إذا كانت الكمية غير متوفرة
    document.getElementById('removeStockForm').addEventListener('submit', function(e) {
        const requestedQuantity = parseFloat(quantityInput.value) || 0;
        if (requestedQuantity > currentStock) {
            e.preventDefault();
            alert('لا يمكن صرف كمية أكبر من المتوفر في المخزون');
        }
    });
});
</script>
{% endblock %}
