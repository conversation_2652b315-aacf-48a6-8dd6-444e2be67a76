from django import forms
from django.forms import inlineformset_factory
from .models import AccountType, Account, JournalEntry, JournalEntryLine

class AccountTypeForm(forms.ModelForm):
    """نموذج إضافة/تعديل أنواع الحسابات"""
    class Meta:
        model = AccountType
        fields = ['name', 'type_category', 'description', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم النوع'}),
            'type_category': forms.Select(attrs={'class': 'form-select'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'وصف النوع'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

class AccountForm(forms.ModelForm):
    """نموذج إضافة/تعديل الحسابات"""
    class Meta:
        model = Account
        fields = ['account_code', 'account_name', 'account_type', 'parent_account', 
                 'description', 'opening_balance', 'is_main_account', 'is_active']
        widgets = {
            'account_code': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'كود الحساب'}),
            'account_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم الحساب'}),
            'account_type': forms.Select(attrs={'class': 'form-select'}),
            'parent_account': forms.Select(attrs={'class': 'form-select'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'وصف الحساب'}),
            'opening_balance': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'is_main_account': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['parent_account'].queryset = Account.objects.filter(is_active=True, is_main_account=True)
        self.fields['parent_account'].empty_label = "لا يوجد حساب أب"

class JournalEntryForm(forms.ModelForm):
    """نموذج إضافة/تعديل القيود المحاسبية"""
    class Meta:
        model = JournalEntry
        fields = ['entry_date', 'description', 'reference_number']
        widgets = {
            'entry_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'وصف القيد'}),
            'reference_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم المرجع'}),
        }

class JournalEntryLineForm(forms.ModelForm):
    """نموذج أسطر القيد المحاسبي"""
    class Meta:
        model = JournalEntryLine
        fields = ['account', 'description', 'debit_amount', 'credit_amount']
        widgets = {
            'account': forms.Select(attrs={'class': 'form-select'}),
            'description': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'وصف السطر'}),
            'debit_amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'credit_amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['account'].queryset = Account.objects.filter(is_active=True)

    def clean(self):
        cleaned_data = super().clean()
        debit_amount = cleaned_data.get('debit_amount', 0)
        credit_amount = cleaned_data.get('credit_amount', 0)

        if debit_amount > 0 and credit_amount > 0:
            raise forms.ValidationError("لا يمكن أن يكون السطر مدين ودائن في نفس الوقت")
        
        if debit_amount == 0 and credit_amount == 0:
            raise forms.ValidationError("يجب أن يحتوي السطر على مبلغ مدين أو دائن")

        return cleaned_data

class AccountSearchForm(forms.Form):
    """نموذج البحث في الحسابات"""
    search = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'البحث بالكود أو الاسم...'
        })
    )
    account_type = forms.ModelChoiceField(
        queryset=AccountType.objects.filter(is_active=True),
        required=False,
        empty_label="جميع أنواع الحسابات",
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    type_category = forms.ChoiceField(
        choices=[('', 'جميع الفئات')] + AccountType.TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    is_main_account = forms.ChoiceField(
        choices=[('', 'جميع الحسابات'), ('True', 'حسابات رئيسية'), ('False', 'حسابات فرعية')],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

class JournalEntrySearchForm(forms.Form):
    """نموذج البحث في القيود المحاسبية"""
    search = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'البحث برقم القيد أو الوصف أو رقم المرجع...'
        })
    )
    status = forms.ChoiceField(
        choices=[('', 'جميع الحالات')] + JournalEntry.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )

# إنشاء Formset لأسطر القيد
JournalEntryLineFormSet = inlineformset_factory(
    JournalEntry, 
    JournalEntryLine, 
    form=JournalEntryLineForm,
    extra=2,
    can_delete=True,
    min_num=2,
    validate_min=True
)
