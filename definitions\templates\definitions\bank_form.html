{% extends 'base.html' %}

{% block title %}{% if action == 'create' %}إضافة بنك جديد{% else %}تعديل البنك{% endif %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1>{% if action == 'create' %}إضافة بنك جديد{% else %}تعديل البنك{% endif %}</h1>
            <p class="text-muted">
                {% if action == 'create' %}إنشاء تعريف بنك جديد في النظام{% else %}تحديث بيانات البنك الموجود{% endif %}
            </p>
        </div>
        <div>
            <a href="{% url 'definitions:bank_list' %}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-2"></i>عودة للقائمة
            </a>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-bank me-2"></i>بيانات البنك
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <!-- Basic Information -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="code" class="form-label">
                                    كود البنك <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       id="code" 
                                       name="code" 
                                       class="form-control" 
                                       value="{% if bank %}{{ bank.code }}{% elif form_data %}{{ form_data.code }}{% endif %}"
                                       required 
                                       placeholder="مثال: NBE">
                                <div class="form-text">كود فريد للبنك (أحرف وأرقام فقط)</div>
                            </div>
                            <div class="col-md-6">
                                <label for="name" class="form-label">
                                    اسم البنك <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       id="name" 
                                       name="name" 
                                       class="form-control" 
                                       value="{% if bank %}{{ bank.name }}{% elif form_data %}{{ form_data.name }}{% endif %}"
                                       required 
                                       placeholder="مثال: البنك الأهلي المصري">
                                <div class="form-text">الاسم الكامل للبنك</div>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="name_en" class="form-label">الاسم بالإنجليزية</label>
                                <input type="text" 
                                       id="name_en" 
                                       name="name_en" 
                                       class="form-control" 
                                       value="{% if bank %}{{ bank.name_en }}{% elif form_data %}{{ form_data.name_en }}{% endif %}"
                                       placeholder="مثال: National Bank of Egypt">
                                <div class="form-text">الاسم الإنجليزي للبنك (اختياري)</div>
                            </div>
                            <div class="col-md-6">
                                <label for="swift_code" class="form-label">SWIFT Code</label>
                                <input type="text" 
                                       id="swift_code" 
                                       name="swift_code" 
                                       class="form-control" 
                                       value="{% if bank %}{{ bank.swift_code }}{% elif form_data %}{{ form_data.swift_code }}{% endif %}"
                                       placeholder="مثال: NBEGEGCX">
                                <div class="form-text">رمز SWIFT للتحويلات الدولية</div>
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" 
                                       id="phone" 
                                       name="phone" 
                                       class="form-control" 
                                       value="{% if bank %}{{ bank.phone }}{% elif form_data %}{{ form_data.phone }}{% endif %}"
                                       placeholder="مثال: 19623">
                                <div class="form-text">رقم الهاتف الرئيسي للبنك</div>
                            </div>
                            <div class="col-md-6">
                                <label for="is_active" class="form-label">حالة البنك</label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="is_active" 
                                           name="is_active"
                                           {% if bank %}{% if bank.is_active %}checked{% endif %}{% elif form_data %}{% if form_data.is_active %}checked{% endif %}{% else %}checked{% endif %}>
                                    <label class="form-check-label" for="is_active">
                                        البنك نشط ومتاح للاستخدام
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-12">
                                <label for="address" class="form-label">العنوان</label>
                                <textarea id="address" 
                                          name="address" 
                                          class="form-control" 
                                          rows="3" 
                                          placeholder="العنوان الكامل للمقر الرئيسي للبنك...">{% if bank %}{{ bank.address }}{% elif form_data %}{{ form_data.address }}{% endif %}</textarea>
                                <div class="form-text">عنوان المقر الرئيسي للبنك</div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{% url 'definitions:bank_list' %}" class="btn btn-secondary">
                                <i class="bi bi-x-circle me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-{% if action == 'create' %}plus-circle{% else %}check-circle{% endif %} me-2"></i>
                                {% if action == 'create' %}إضافة البنك{% else %}حفظ التغييرات{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تنظيف كود البنك
    const codeInput = document.getElementById('code');
    if (codeInput) {
        codeInput.addEventListener('input', function() {
            this.value = this.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
        });
    }
    
    // تنظيف SWIFT Code
    const swiftInput = document.getElementById('swift_code');
    if (swiftInput) {
        swiftInput.addEventListener('input', function() {
            this.value = this.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
            if (this.value.length > 11) {
                this.value = this.value.substring(0, 11);
            }
        });
    }
});
</script>
{% endblock %}
