{% extends 'base.html' %}
{% load static %}

{% block title %}مجموعات الأصول - أوساريك{% endblock %}

{% block extra_css %}
<style>
    body {
        background: #ffffff;
        min-height: 100vh;
    }

    .page-header {
        background: #ffffff;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-bottom: 1px solid #e0e0e0;
    }

    .page-title {
        font-size: 2rem;
        font-weight: 800;
        color: #333;
        margin: 0;
    }

    .page-subtitle {
        color: #666;
        margin: 0.5rem 0 0 0;
    }

    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: #ffffff;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 800;
        color: #007bff;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #666;
        font-size: 0.9rem;
    }

    .table-container {
        background: #ffffff;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .table {
        margin: 0;
        font-size: 0.875rem;
    }

    .table th {
        background: #f8f9fa;
        border-bottom: 2px solid #e0e0e0;
        color: #333;
        font-weight: 600;
        padding: 1rem 0.75rem;
    }

    .table td {
        padding: 0.75rem;
        vertical-align: middle;
        border-bottom: 1px solid #f0f0f0;
    }

    .table tbody tr:hover {
        background: #f8f9fa;
    }

    .btn {
        padding: 0.5rem 1rem;
        border-radius: 6px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        font-size: 0.8rem;
    }

    .btn-primary {
        background: #007bff;
        color: white;
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
    }

    .btn-primary:hover {
        background: #0056b3;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-outline-secondary {
        background: transparent;
        border: 1px solid #6c757d;
        color: #6c757d;
    }

    .btn-outline-secondary:hover {
        background: #6c757d;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    .btn-outline-info {
        background: transparent;
        border: 1px solid #17a2b8;
        color: #17a2b8;
    }

    .btn-outline-info:hover {
        background: #17a2b8;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    .btn-outline-primary {
        background: transparent;
        border: 1px solid #007bff;
        color: #007bff;
    }

    .btn-outline-primary:hover {
        background: #007bff;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    .btn-outline-danger {
        background: transparent;
        border: 1px solid #dc3545;
        color: #dc3545;
    }

    .btn-outline-danger:hover {
        background: #dc3545;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    .badge {
        padding: 0.4rem 0.8rem;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .badge.active {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .badge.inactive {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        color: #666;
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: #ccc;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- CSRF Token for JavaScript -->
    {% csrf_token %}
    
    <!-- Page Header -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="page-title">
                    <i class="bi bi-collection me-2"></i>مجموعات الأصول
                </h1>
                <p class="page-subtitle">إدارة مجموعات الأصول الثابتة في النظام</p>
            </div>
            <div>
                <a href="{% url 'definitions:dashboard' %}" class="btn btn-outline-secondary me-2">
                    <i class="bi bi-arrow-right me-2"></i>عودة للتعريفات
                </a>
                <button type="button" class="btn btn-primary" onclick="addAssetGroup()">
                    <i class="bi bi-plus-circle me-2"></i>إضافة مجموعة أصول
                </button>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-cards">
        <div class="stat-card">
            <div class="stat-number">{{ total_groups }}</div>
            <div class="stat-label">إجمالي المجموعات</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ active_groups }}</div>
            <div class="stat-label">المجموعات النشطة</div>
        </div>
    </div>

    <!-- Messages -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                <i class="bi bi-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% else %}info-circle{% endif %} me-2"></i>
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- Asset Groups Table -->
    <div class="table-container">
        {% if asset_groups %}
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الكود</th>
                        <th>الاسم</th>
                        <th>الوصف</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for group in asset_groups %}
                        <tr>
                            <td>
                                <strong>{{ group.code }}</strong>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ group.name }}</strong>
                                    {% if group.name_en %}
                                        <br><small class="text-muted">{{ group.name_en }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                {% if group.description %}
                                    {{ group.description|truncatechars:50 }}
                                {% else %}
                                    <span class="text-muted">لا يوجد وصف</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge {% if group.is_active %}active{% else %}inactive{% endif %}">
                                    {% if group.is_active %}
                                        <i class="bi bi-check-circle me-1"></i>نشط
                                    {% else %}
                                        <i class="bi bi-x-circle me-1"></i>غير نشط
                                    {% endif %}
                                </span>
                            </td>
                            <td>
                                <div class="d-flex gap-1">
                                    <a href="{% url 'definitions:asset_group_detail' group.id %}" 
                                       class="btn btn-sm btn-outline-info" 
                                       title="عرض التفاصيل">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{% url 'definitions:asset_group_edit' group.id %}" 
                                       class="btn btn-sm btn-outline-primary" 
                                       title="تعديل">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <button type="button" 
                                            class="btn btn-sm btn-outline-danger" 
                                            title="حذف"
                                            onclick="deleteAssetGroup({{ group.id }}, '{{ group.name|escapejs }}')">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        {% else %}
            <div class="empty-state">
                <i class="bi bi-collection"></i>
                <h3>لا توجد مجموعات أصول</h3>
                <p>لم يتم العثور على مجموعات أصول مطابقة للبحث.</p>
                <a href="{% url 'definitions:asset_group_create' %}" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>إضافة أول مجموعة أصول
                </a>
            </div>
        {% endif %}
    </div>
</div>

<script>
function addAssetGroup() {
    console.log('🚀 addAssetGroup called');

    var code = prompt('أدخل كود مجموعة الأصول (مثال: COMP، FURN، VEHI):');
    if (code && code.trim()) {
        code = code.trim().toUpperCase();
        console.log('📝 Code entered:', code);

        var name = prompt('أدخل اسم مجموعة الأصول:');
        if (name && name.trim()) {
            name = name.trim();
            console.log('📝 Name entered:', name);

            // التأكد من الإضافة
            if (confirm('هل تريد إضافة مجموعة الأصول "' + name + '" بالكود "' + code + '"؟')) {
                console.log('✅ User confirmed');

                // إنشاء نموذج مخفي وإرساله
                var form = document.createElement('form');
                form.method = 'POST';
                form.action = '/definitions/asset-groups/quick-create/';

                // إضافة CSRF token
                var csrfTokenElement = document.querySelector('[name=csrfmiddlewaretoken]');
                console.log('🔍 CSRF token element:', csrfTokenElement);

                if (!csrfTokenElement) {
                    alert('خطأ: لم يتم العثور على CSRF token');
                    console.error('❌ CSRF token not found');
                    return;
                }

                var csrfToken = csrfTokenElement.value;
                console.log('🔑 CSRF token:', csrfToken);

                var csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = 'csrfmiddlewaretoken';
                csrfInput.value = csrfToken;
                form.appendChild(csrfInput);

                // إضافة الكود
                var codeInput = document.createElement('input');
                codeInput.type = 'hidden';
                codeInput.name = 'code';
                codeInput.value = code;
                form.appendChild(codeInput);

                // إضافة الاسم
                var nameInput = document.createElement('input');
                nameInput.type = 'hidden';
                nameInput.name = 'name';
                nameInput.value = name;
                form.appendChild(nameInput);

                console.log('📤 Submitting form to:', form.action);

                // إضافة النموذج للصفحة وإرساله
                document.body.appendChild(form);
                form.submit();
            } else {
                console.log('❌ User cancelled');
            }
        } else {
            console.log('❌ No name entered');
        }
    } else {
        console.log('❌ No code entered');
    }
}

function deleteAssetGroup(groupId, groupName) {
    if (confirm('هل أنت متأكد من حذف مجموعة الأصول "' + groupName + '"؟\n\nهذا الإجراء لا يمكن التراجع عنه!')) {
        // إنشاء نموذج مخفي وإرساله
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = '/definitions/asset-groups/' + groupId + '/quick-delete/';

        // إضافة CSRF token
        var csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
        var csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrfmiddlewaretoken';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);

        // إضافة النموذج للصفحة وإرساله
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
