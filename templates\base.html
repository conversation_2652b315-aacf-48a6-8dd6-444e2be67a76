{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}أوساريك - نظام إدارة الحسابات والمخزون{% endblock %}</title>
    
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.umd.min.js"></script>
    
    <style>
        :root {
            --primary-color: #0d6efd;
            --secondary-color: #0dcaf0;
            --success-color: #198754;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #0dcaf0;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --sidebar-width: 280px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            width: var(--sidebar-width);
            height: 100vh;
            background: linear-gradient(180deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            z-index: 1000;
            transition: transform 0.3s ease;
            overflow-y: auto;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar.collapsed {
            transform: translateX(100%);
        }

        .sidebar-header {
            padding: 2rem 1.5rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .sidebar-header h3 {
            font-weight: 700;
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
        }

        .sidebar-header p {
            font-size: 0.9rem;
            opacity: 0.8;
            margin: 0;
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-item {
            margin: 0.25rem 1rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-link:hover, .nav-link.active {
            background: rgba(255,255,255,0.15);
            color: white;
            transform: translateX(-5px);
        }

        .nav-link i {
            margin-left: 0.75rem;
            font-size: 1.1rem;
            width: 20px;
            text-align: center;
        }

        /* Main Content */
        .main-content {
            margin-right: var(--sidebar-width);
            min-height: 100vh;
            transition: margin-right 0.3s ease;
        }

        .main-content.expanded {
            margin-right: 0;
        }

        /* Top Navigation */
        .top-nav {
            background: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 999;
        }

        .nav-toggle {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--primary-color);
            cursor: pointer;
        }

        .search-container {
            flex: 1;
            max-width: 400px;
            margin: 0 2rem;
        }

        .search-input {
            width: 100%;
            padding: 0.5rem 1rem;
            border: 1px solid #ddd;
            border-radius: 25px;
            font-size: 0.9rem;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        /* Content Area */
        .content-area {
            padding: 2rem;
        }

        .page-header {
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: #6c757d;
            font-size: 1rem;
        }

        /* Cards */
        .card {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        /* Buttons */
        .btn {
            border-radius: 0.5rem;
            font-weight: 600;
            padding: 0.5rem 1.5rem;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background: #0b5ed7;
            border-color: #0a58ca;
            transform: translateY(-1px);
        }

        /* Tables */
        .table {
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .table thead th {
            background: var(--primary-color);
            color: white;
            border: none;
            font-weight: 600;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
            }
            
            .nav-toggle {
                display: block;
            }
            
            .search-container {
                display: none;
            }
            
            .content-area {
                padding: 1rem;
            }
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-out;
        }

        /* Custom Scrollbar */
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255,255,255,0.5);
        }

        /* Notification Badge */
        .notification-badge {
            position: absolute;
            top: -5px;
            left: -5px;
            background: var(--danger-color);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Loading Spinner */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h3>أوساريك</h3>
            <p>نظام إدارة الحسابات والمخزون</p>
        </div>
        
        <div class="sidebar-nav">
            <div class="nav-item">
                <a href="{% url 'dashboard_home' %}" class="nav-link {% if request.resolver_match.url_name == 'dashboard_home' %}active{% endif %}">
                    <i class="bi bi-house-door"></i>
                    <span>الرئيسية</span>
                </a>
            </div>

            <!-- التعريفات -->
            <div class="nav-item">
                <a href="{% url 'definitions:dashboard' %}" class="nav-link {% if request.resolver_match.namespace == 'definitions' %}active{% endif %}">
                    <i class="bi bi-gear-fill"></i>
                    <span>التعريفات</span>
                </a>
            </div>

            <!-- ادارة المخازن -->
            <div class="nav-item">
                <a href="{% url 'warehouses:dashboard' %}" class="nav-link {% if request.resolver_match.namespace == 'warehouses' %}active{% endif %}">
                    <i class="bi bi-boxes"></i>
                    <span>ادارة المخازن</span>
                </a>
            </div>

            <!-- التصنيع -->
            <div class="nav-item">
                <a href="{% url 'manufacturing:dashboard' %}" class="nav-link {% if request.resolver_match.namespace == 'manufacturing' %}active{% endif %}">
                    <i class="bi bi-gear-wide-connected"></i>
                    <span>التصنيع</span>
                </a>
            </div>

            <!-- المبيعات -->
            <div class="nav-item">
                <a href="{% url 'sales:dashboard' %}" class="nav-link {% if request.resolver_match.namespace == 'sales' %}active{% endif %}">
                    <i class="bi bi-cart-plus"></i>
                    <span>المبيعات</span>
                </a>
            </div>

            <!-- المشتريات -->
            <div class="nav-item">
                <a href="{% url 'purchases:dashboard' %}" class="nav-link {% if request.resolver_match.namespace == 'purchases' %}active{% endif %}">
                    <i class="bi bi-cart-check"></i>
                    <span>المشتريات</span>
                </a>
            </div>

            <!-- الاصول الثابته -->
            <div class="nav-item">
                <a href="{% url 'assets:dashboard' %}" class="nav-link {% if request.resolver_match.namespace == 'assets' %}active{% endif %}">
                    <i class="bi bi-building"></i>
                    <span>الاصول الثابته</span>
                </a>
            </div>

            <!-- البنوك -->
            <div class="nav-item">
                <a href="{% url 'banks:dashboard' %}" class="nav-link {% if request.resolver_match.namespace == 'banks' %}active{% endif %}">
                    <i class="bi bi-bank"></i>
                    <span>البنوك</span>
                </a>
            </div>

            <!-- الخزائن -->
            <div class="nav-item">
                <a href="{% url 'treasuries:dashboard' %}" class="nav-link {% if request.resolver_match.namespace == 'treasuries' %}active{% endif %}">
                    <i class="bi bi-safe"></i>
                    <span>الخزائن</span>
                </a>
            </div>

            <!-- الحسابات العامه -->
            <div class="nav-item">
                <a href="{% url 'accounting:dashboard' %}" class="nav-link {% if request.resolver_match.namespace == 'accounting' %}active{% endif %}">
                    <i class="bi bi-calculator"></i>
                    <span>الحسابات العامه</span>
                </a>
            </div>

            <!-- المركز الرئيسى والفروع -->
            <div class="nav-item">
                <a href="{% url 'branches:dashboard' %}" class="nav-link {% if request.resolver_match.namespace == 'branches' %}active{% endif %}">
                    <i class="bi bi-diagram-3"></i>
                    <span>المركز الرئيسى والفروع</span>
                </a>
            </div>

            <!-- شؤون العاملين -->
            <div class="nav-item">
                <a href="{% url 'hr:dashboard' %}" class="nav-link {% if request.resolver_match.namespace == 'hr' %}active{% endif %}">
                    <i class="bi bi-people"></i>
                    <span>شؤون العاملين</span>
                </a>
            </div>

            <!-- التقارير -->
            <div class="nav-item">
                <a href="{% url 'reports:dashboard' %}" class="nav-link {% if request.resolver_match.namespace == 'reports' %}active{% endif %}">
                    <i class="bi bi-graph-up"></i>
                    <span>التقارير</span>
                </a>
            </div>

            <!-- الاعدادات والخدمات -->
            <div class="nav-item">
                <a href="{% url 'settings:dashboard' %}" class="nav-link {% if request.resolver_match.namespace == 'settings' %}active{% endif %}">
                    <i class="bi bi-gear"></i>
                    <span>الاعدادات والخدمات</span>
                </a>
            </div>

            <div class="nav-item">
                <a href="/accounts/logout/" class="nav-link">
                    <i class="bi bi-box-arrow-left"></i>
                    <span>تسجيل الخروج</span>
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <!-- Top Navigation -->
        <nav class="top-nav">
            <button class="nav-toggle" id="navToggle">
                <i class="bi bi-list"></i>
            </button>
            
            <div class="search-container">
                <input type="text" class="search-input" placeholder="البحث في النظام...">
            </div>
            
            <div class="user-info">
                <div class="user-avatar">
                    {{ user.first_name.0|default:user.username.0|upper }}
                </div>
                <div>
                    <div style="font-weight: 600;">{{ user.get_full_name|default:user.username }}</div>
                    <div style="font-size: 0.8rem; color: #6c757d;">{{ user.email|default:"مستخدم" }}</div>
                </div>
            </div>
        </nav>

        <!-- Content Area -->
        <div class="content-area">
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
            
            {% block content %}{% endblock %}
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Sidebar Toggle
        const navToggle = document.getElementById('navToggle');
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');

        navToggle.addEventListener('click', function() {
            sidebar.classList.toggle('show');
        });

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            if (window.innerWidth <= 768) {
                if (!sidebar.contains(event.target) && !navToggle.contains(event.target)) {
                    sidebar.classList.remove('show');
                }
            }
        });

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);

        // Add loading state to buttons
        document.addEventListener('click', function(event) {
            if (event.target.matches('button[type="submit"], .btn-submit')) {
                const button = event.target;
                const originalText = button.innerHTML;
                button.innerHTML = '<span class="loading-spinner"></span> جاري المعالجة...';
                button.disabled = true;
                
                // Re-enable after 3 seconds (fallback)
                setTimeout(function() {
                    button.innerHTML = originalText;
                    button.disabled = false;
                }, 3000);
            }
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
