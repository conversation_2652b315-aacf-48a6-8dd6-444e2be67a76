from django.urls import path
from . import views

app_name = 'definitions'

urlpatterns = [
    # لوحة التحكم
    path('', views.definitions_dashboard, name='dashboard'),

    # أماكن الأصناف في المخازن
    path('warehouse-locations/', views.warehouse_location_list, name='warehouse_location_list'),
    path('warehouse-locations/create/', views.warehouse_location_create, name='warehouse_location_create'),
    path('warehouse-locations/<int:location_id>/', views.warehouse_location_detail, name='warehouse_location_detail'),
    path('warehouse-locations/<int:location_id>/edit/', views.warehouse_location_edit, name='warehouse_location_edit'),
    path('warehouse-locations/<int:location_id>/delete/', views.warehouse_location_delete, name='warehouse_location_delete'),

    # إدارة الأصناف
    path('products/', views.product_list, name='product_list'),
    path('products/create/', views.product_create, name='product_create'),
    path('products/<int:product_id>/', views.product_detail, name='product_detail'),
    path('products/<int:product_id>/edit/', views.product_edit, name='product_edit'),
    path('products/<int:product_id>/delete/', views.product_delete, name='product_delete'),
    path('products/<int:product_id>/duplicate/', views.product_duplicate, name='product_duplicate'),

    # تعريف المخازن
    path('warehouses/', views.warehouse_list, name='warehouse_list'),
    path('warehouses/create/', views.warehouse_create, name='warehouse_create'),
    path('warehouses/<int:warehouse_id>/', views.warehouse_detail, name='warehouse_detail'),
    path('warehouses/<int:warehouse_id>/edit/', views.warehouse_edit, name='warehouse_edit'),
    path('warehouses/<int:warehouse_id>/delete/', views.warehouse_delete, name='warehouse_delete'),
    path('warehouses/<int:warehouse_id>/quick-delete/', views.warehouse_quick_delete, name='warehouse_quick_delete'),

    # تعريف العملات
    path('currencies/', views.currency_list, name='currency_list'),
    path('currencies/create/', views.currency_create, name='currency_create'),
    path('currencies/<int:currency_id>/', views.currency_detail, name='currency_detail'),
    path('currencies/<int:currency_id>/edit/', views.currency_edit, name='currency_edit'),
    path('currencies/<int:currency_id>/delete/', views.currency_delete, name='currency_delete'),
    path('currencies/<int:currency_id>/quick-delete/', views.currency_quick_delete, name='currency_quick_delete'),

    # تعريف البنوك
    path('banks/', views.bank_list, name='bank_list'),
    path('banks/create/', views.bank_create, name='bank_create'),
    path('banks/<int:bank_id>/', views.bank_detail, name='bank_detail'),
    path('banks/<int:bank_id>/edit/', views.bank_edit, name='bank_edit'),
    path('banks/<int:bank_id>/delete/', views.bank_delete, name='bank_delete'),
    path('banks/<int:bank_id>/quick-delete/', views.bank_quick_delete, name='bank_quick_delete'),

    # تعريف وحدات القياس
    path('units/', views.unit_list, name='unit_list'),
    path('units/create/', views.unit_create, name='unit_create'),
    path('units/<int:unit_id>/', views.unit_detail, name='unit_detail'),
    path('units/<int:unit_id>/edit/', views.unit_edit, name='unit_edit'),
    path('units/<int:unit_id>/delete/', views.unit_delete, name='unit_delete'),
    path('units/<int:unit_id>/quick-delete/', views.unit_quick_delete, name='unit_quick_delete'),

    # تعريف مجموعات الأصول
    path('asset-groups/', views.asset_group_list, name='asset_group_list'),
    path('asset-groups/create/', views.asset_group_create, name='asset_group_create'),
    path('asset-groups/quick-create/', views.asset_group_quick_create, name='asset_group_quick_create'),
    path('asset-groups/<int:group_id>/', views.asset_group_detail, name='asset_group_detail'),
    path('asset-groups/<int:group_id>/edit/', views.asset_group_edit, name='asset_group_edit'),
    path('asset-groups/<int:group_id>/delete/', views.asset_group_delete, name='asset_group_delete'),
    path('asset-groups/<int:group_id>/quick-delete/', views.asset_group_quick_delete, name='asset_group_quick_delete'),

    # تعريف ماركات الأصول
    path('asset-brands/', views.asset_brand_list, name='asset_brand_list'),
    path('asset-brands/create/', views.asset_brand_create, name='asset_brand_create'),
    path('asset-brands/<int:brand_id>/', views.asset_brand_detail, name='asset_brand_detail'),
    path('asset-brands/<int:brand_id>/edit/', views.asset_brand_edit, name='asset_brand_edit'),
    path('asset-brands/<int:brand_id>/delete/', views.asset_brand_delete, name='asset_brand_delete'),
    path('asset-brands/<int:brand_id>/quick-delete/', views.asset_brand_quick_delete, name='asset_brand_quick_delete'),

    # تعريف أنواع المصروفات
    path('expense-types/', views.expense_type_list, name='expense_type_list'),
    path('expense-types/create/', views.expense_type_create, name='expense_type_create'),
    path('expense-types/<int:type_id>/', views.expense_type_detail, name='expense_type_detail'),
    path('expense-types/<int:type_id>/edit/', views.expense_type_edit, name='expense_type_edit'),
    path('expense-types/<int:type_id>/delete/', views.expense_type_delete, name='expense_type_delete'),
    path('expense-types/<int:type_id>/quick-delete/', views.expense_type_quick_delete, name='expense_type_quick_delete'),

    # تعريف أسماء المصروفات
    path('expense-names/', views.expense_name_list, name='expense_name_list'),
    path('expense-names/create/', views.expense_name_create, name='expense_name_create'),
    path('expense-names/<int:name_id>/', views.expense_name_detail, name='expense_name_detail'),
    path('expense-names/<int:name_id>/edit/', views.expense_name_edit, name='expense_name_edit'),
    path('expense-names/<int:name_id>/delete/', views.expense_name_delete, name='expense_name_delete'),
    path('expense-names/<int:name_id>/quick-delete/', views.expense_name_quick_delete, name='expense_name_quick_delete'),

    # تعريف أنواع الإيرادات
    path('revenue-types/', views.revenue_type_list, name='revenue_type_list'),
    path('revenue-types/create/', views.revenue_type_create, name='revenue_type_create'),
    path('revenue-types/<int:type_id>/', views.revenue_type_detail, name='revenue_type_detail'),
    path('revenue-types/<int:type_id>/edit/', views.revenue_type_edit, name='revenue_type_edit'),
    path('revenue-types/<int:type_id>/delete/', views.revenue_type_delete, name='revenue_type_delete'),
    path('revenue-types/<int:type_id>/quick-delete/', views.revenue_type_quick_delete, name='revenue_type_quick_delete'),

    # تعريف أسماء الإيرادات
    path('revenue-names/', views.revenue_name_list, name='revenue_name_list'),
    path('revenue-names/create/', views.revenue_name_create, name='revenue_name_create'),
    path('revenue-names/<int:name_id>/', views.revenue_name_detail, name='revenue_name_detail'),
    path('revenue-names/<int:name_id>/edit/', views.revenue_name_edit, name='revenue_name_edit'),
    path('revenue-names/<int:name_id>/delete/', views.revenue_name_delete, name='revenue_name_delete'),
    path('revenue-names/<int:name_id>/quick-delete/', views.revenue_name_quick_delete, name='revenue_name_quick_delete'),

    # فئات الأصناف
    path('categories/', views.category_list, name='category_list'),
    path('categories/create/', views.category_create, name='category_create'),
    path('categories/<int:category_id>/', views.category_detail, name='category_detail'),
    path('categories/<int:category_id>/edit/', views.category_edit, name='category_edit'),
    path('categories/<int:category_id>/delete/', views.category_delete, name='category_delete'),
    path('categories/<int:category_id>/quick-delete/', views.category_quick_delete, name='category_quick_delete'),

    # أكواد الأصناف
    path('product-codes/', views.product_code_list, name='product_code_list'),
    path('product-codes/create/', views.product_code_create, name='product_code_create'),
    path('product-codes/<int:code_id>/', views.product_code_detail, name='product_code_detail'),
    path('product-codes/<int:code_id>/edit/', views.product_code_edit, name='product_code_edit'),
    path('product-codes/<int:code_id>/delete/', views.product_code_delete, name='product_code_delete'),

    # تعريف الأشخاص
    path('persons/', views.person_list, name='person_list'),
    path('persons/create/', views.person_create, name='person_create'),
    path('persons/<int:person_id>/', views.person_detail, name='person_detail'),
    path('persons/<int:person_id>/edit/', views.person_edit, name='person_edit'),
    path('persons/<int:person_id>/delete/', views.person_delete, name='person_delete'),

    # تعريف مراكز الربحية
    path('profit-centers/', views.profit_center_list, name='profit_center_list'),
    path('profit-centers/create/', views.profit_center_create, name='profit_center_create'),
    path('profit-centers/<int:center_id>/', views.profit_center_detail, name='profit_center_detail'),
    path('profit-centers/<int:center_id>/edit/', views.profit_center_edit, name='profit_center_edit'),
    path('profit-centers/<int:center_id>/delete/', views.profit_center_delete, name='profit_center_delete'),
    path('profit-centers/<int:center_id>/quick-delete/', views.profit_center_quick_delete, name='profit_center_quick_delete'),

    # تعريف الطابعات
    path('printers/', views.printer_list, name='printer_list'),
    path('printers/create/', views.printer_create, name='printer_create'),
    path('printers/<int:printer_id>/', views.printer_detail, name='printer_detail'),
    path('printers/<int:printer_id>/edit/', views.printer_edit, name='printer_edit'),
    path('printers/<int:printer_id>/delete/', views.printer_delete, name='printer_delete'),
    path('printers/<int:printer_id>/quick-delete/', views.printer_quick_delete, name='printer_quick_delete'),

]
