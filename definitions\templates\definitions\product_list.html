{% extends 'base.html' %}
{% load static %}

{% block title %}إدارة الأصناف{% endblock %}

{% block extra_css %}
<style>
    /* Modern Enhanced Design */
    :root {
        --primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --primary-solid: #667eea;
        --secondary: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --success: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        --warning: linear-gradient(135deg, #f7971e 0%, #ffd200 100%);
        --danger: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
        --info: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);

        /* Enhanced Glass Effects */
        --glass-bg: rgba(255, 255, 255, 0.15);
        --glass-border: rgba(255, 255, 255, 0.2);
        --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        --glass-backdrop: blur(10px);

        /* Enhanced Colors */
        --text-primary: #1a202c;
        --text-secondary: #2d3748;
        --text-muted: #4a5568;
        --bg-primary: #f7fafc;
        --bg-secondary: #edf2f7;

        --radius: 12px;
        --radius-lg: 20px;
    }

    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #11998e 50%, #38ef7d 75%, #56ab2f 100%);
        background-attachment: fixed;
        background-size: 400% 400%;
        animation: gradientShift 15s ease infinite;
        font-size: 13px;
        line-height: 1.4;
        color: var(--text-primary);
        min-height: 100vh;
    }

    @keyframes gradientShift {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    /* Header */
    .page-header {
        background: var(--glass-bg);
        backdrop-filter: var(--glass-backdrop);
        border: 1px solid var(--glass-border);
        border-radius: var(--radius-lg);
        box-shadow: var(--glass-shadow);
        padding: 1.5rem 0;
        margin-bottom: 1.5rem;
        margin-top: 1rem;
    }

    .page-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: white;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    /* Stats Cards */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.25);
        backdrop-filter: var(--glass-backdrop);
        border: 1px solid rgba(255, 255, 255, 0.3);
        padding: 1.5rem;
        border-radius: var(--radius-lg);
        box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        position: relative;
        overflow: hidden;
        transition: all 0.4s ease;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: var(--primary);
        border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    }

    .stat-card:nth-child(1)::before {
        background: var(--primary);
    }

    .stat-card:nth-child(2)::before {
        background: var(--success);
    }

    .stat-card:nth-child(3)::before {
        background: var(--warning);
    }

    .stat-card:nth-child(4)::before {
        background: var(--danger);
    }

    .stat-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 15px 45px rgba(31, 38, 135, 0.6);
        background: rgba(255, 255, 255, 0.35);
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 800;
        color: white;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .stat-label {
        font-size: 0.875rem;
        color: rgba(255, 255, 255, 0.9);
        font-weight: 500;
    }

    /* Filters */
    .filters-section {
        background: var(--glass-bg);
        backdrop-filter: var(--glass-backdrop);
        border: 1px solid var(--glass-border);
        padding: 1.5rem;
        border-radius: var(--radius-lg);
        box-shadow: var(--glass-shadow);
        margin-bottom: 1.5rem;
    }

    .filters-row {
        display: grid;
        grid-template-columns: 2fr 1fr auto;
        gap: 1rem;
        align-items: end;
    }

    .form-group {
        margin-bottom: 0;
    }

    .form-label {
        font-size: 0.875rem;
        font-weight: 600;
        color: white;
        margin-bottom: 0.5rem;
        display: block;
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    }

    .form-control {
        width: 100%;
        padding: 0.75rem;
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: var(--radius);
        font-size: 0.875rem;
        color: white;
        backdrop-filter: blur(4px);
        transition: all 0.3s ease;
    }

    .form-control::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .form-control:focus {
        outline: none;
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
    }

    .btn {
        padding: 0.75rem 1.5rem;
        border-radius: var(--radius);
        font-size: 0.875rem;
        font-weight: 600;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        position: relative;
        overflow: hidden;
    }

    .btn-primary {
        background: var(--primary);
        color: white;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
    }

    .btn-outline {
        background: rgba(255, 255, 255, 0.15);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(8px);
        transition: all 0.3s ease;
    }

    .btn-outline:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 255, 255, 0.2);
        color: white;
    }

    /* Enhanced Table */
    .table-container {
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: var(--radius-lg);
        box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .table-header {
        padding: 1.5rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.3);
        display: flex;
        justify-content: between;
        align-items: center;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.3) 0%, rgba(118, 75, 162, 0.3) 100%);
    }

    .table-title {
        font-size: 1.25rem;
        font-weight: 700;
        color: white;
        text-shadow: 0 2px 4px rgba(0,0,0,0.4);
    }

    .table {
        width: 100%;
        border-collapse: collapse;
        font-size: 0.875rem;
        margin: 0;
    }

    .table th {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
        padding: 1rem 0.75rem;
        text-align: center;
        font-weight: 700;
        color: white;
        border-bottom: 2px solid rgba(255, 255, 255, 0.3);
        text-shadow: 0 1px 2px rgba(0,0,0,0.4);
        font-size: 0.85rem;
    }

    .table td {
        padding: 1rem 0.75rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.15);
        color: white;
        background: rgba(255, 255, 255, 0.08);
        text-align: center;
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    }

    .table tbody tr:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(1.005);
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(31, 38, 135, 0.3);
    }

    /* Product Image */
    .product-image {
        width: 40px;
        height: 40px;
        border-radius: var(--radius);
        object-fit: cover;
        border: 1px solid var(--gray-200);
    }

    /* Enhanced Status Badges */
    .badge {
        display: inline-flex;
        align-items: center;
        padding: 0.4rem 0.8rem;
        border-radius: 25px;
        font-size: 0.75rem;
        font-weight: 600;
        backdrop-filter: blur(8px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        text-shadow: 0 1px 2px rgba(0,0,0,0.4);
        transition: all 0.3s ease;
        white-space: nowrap;
    }

    .badge:hover {
        transform: translateY(-1px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.3);
    }

    .badge-success {
        background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(86, 171, 47, 0.4);
    }

    .badge-danger {
        background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(255, 65, 108, 0.4);
    }

    .badge-secondary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    /* Actions */
    .action-buttons {
        display: flex;
        gap: 0.5rem;
    }

    .btn-sm {
        padding: 0.375rem 0.75rem;
        font-size: 0.75rem;
    }

    /* Pagination */
    .pagination-container {
        padding: 1.5rem;
        border-top: 1px solid rgba(255, 255, 255, 0.2);
        display: flex;
        justify-content: center;
        background: rgba(255, 255, 255, 0.05);
    }

    .pagination {
        display: flex;
        gap: 0.5rem;
        list-style: none;
        margin: 0;
        padding: 0;
    }

    .page-link {
        padding: 0.75rem 1rem;
        color: white;
        text-decoration: none;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: var(--radius);
        font-size: 0.875rem;
        font-weight: 500;
        backdrop-filter: blur(4px);
        transition: all 0.3s ease;
    }

    .page-link:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-2px);
    }

    .page-item.active .page-link {
        background: var(--primary);
        border-color: rgba(255, 255, 255, 0.5);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    /* Additional Effects */
    .product-image {
        width: 45px;
        height: 45px;
        border-radius: var(--radius);
        object-fit: cover;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        transition: all 0.3s ease;
    }

    .product-image:hover {
        transform: scale(1.1);
        box-shadow: 0 8px 16px rgba(0,0,0,0.3);
    }

    .action-buttons .btn-sm {
        padding: 0.5rem;
        border-radius: 8px;
        backdrop-filter: blur(4px);
        transition: all 0.3s ease;
    }

    .action-buttons .btn-sm:hover {
        transform: scale(1.1);
    }

    .text-danger {
        color: #ff6b6b !important;
    }

    /* Floating Animation */
    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    .stat-card:nth-child(odd) {
        animation: float 6s ease-in-out infinite;
    }

    .stat-card:nth-child(even) {
        animation: float 6s ease-in-out infinite reverse;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .filters-row {
            grid-template-columns: 1fr;
        }

        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }

        .table-container {
            overflow-x: auto;
        }

        .stat-card {
            padding: 1rem;
        }

        .stat-number {
            font-size: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <h1 class="page-title">
                <i class="bi bi-box-seam me-2"></i>
                إدارة الأصناف
            </h1>
            <div class="d-flex gap-2">
                <a href="{% url 'definitions:dashboard' %}" class="btn btn-outline">
                    <i class="bi bi-arrow-left me-2"></i>
                    عودة للتعريفات
                </a>
                <a href="{% url 'definitions:product_create' %}" class="btn btn-primary">
                    <i class="bi bi-plus-lg me-2"></i>
                    إضافة صنف جديد
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ total_products }}</div>
            <div class="stat-label">إجمالي الأصناف</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ active_products }}</div>
            <div class="stat-label">أصناف نشطة</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ low_stock_products }}</div>
            <div class="stat-label">مخزون منخفض</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ out_of_stock_products }}</div>
            <div class="stat-label">نفد المخزون</div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filters-section">
        <form method="get" class="filters-form">
            <div class="filters-row">
                <div class="form-group">
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" 
                           placeholder="البحث بالاسم أو الكود أو الباركود..." 
                           value="{{ search_query }}">
                </div>
                <div class="form-group">
                    <label class="form-label">الفئة</label>
                    <select name="category" class="form-control">
                        <option value="">جميع الفئات</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}" {% if category.id|stringformat:"s" == category_id %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search"></i>
                        بحث
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Products Table -->
    <div class="table-container">
        <div class="table-header">
            <div class="table-title">قائمة الأصناف</div>
        </div>

        <table class="table">
            <thead>
                <tr>
                    <th>الصورة</th>
                    <th>الكود</th>
                    <th>اسم الصنف</th>
                    <th>الفئة</th>
                    <th>نوع الصنف</th>
                    <th>سعر التكلفة</th>
                    <th>سعر البيع</th>
                    <th>الحالة</th>
                    <th>العمليات</th>
                </tr>
            </thead>
            <tbody>
                {% for product in page_obj %}
                <tr>
                    <td>
                        {% if product.image %}
                            <img src="{{ product.image.url }}" alt="{{ product.name }}" class="product-image">
                        {% else %}
                            <div class="product-image bg-light d-flex align-items-center justify-content-center">
                                <i class="bi bi-image text-muted"></i>
                            </div>
                        {% endif %}
                    </td>
                    <td>
                        <strong>{{ product.code }}</strong>
                        {% if product.barcode %}
                            <br><small class="text-muted">{{ product.barcode }}</small>
                        {% endif %}
                    </td>
                    <td>
                        <div>{{ product.name }}</div>
                        {% if product.name_en %}
                            <small class="text-muted">{{ product.name_en }}</small>
                        {% endif %}
                    </td>
                    <td>
                        {% if product.category %}
                            <span class="badge badge-secondary">{{ product.category.name }}</span>
                        {% else %}
                            <span class="text-muted">غير محدد</span>
                        {% endif %}
                    </td>
                    <td>{{ product.get_product_type_display }}</td>
                    <td>
                        {% if product.cost_price %}
                            {{ product.cost_price|floatformat:2 }} ج.م
                        {% else %}
                            <span class="text-muted">-</span>
                        {% endif %}
                    </td>
                    <td>
                        {% if product.selling_price %}
                            <strong>{{ product.selling_price|floatformat:2 }} ج.م</strong>
                        {% else %}
                            <span class="text-muted">مجاني</span>
                        {% endif %}
                    </td>
                    <td>
                        {% if product.is_active %}
                            <span class="badge badge-success">نشط</span>
                        {% else %}
                            <span class="badge badge-danger">غير نشط</span>
                        {% endif %}
                    </td>
                    <td>
                        <div class="action-buttons">
                            <a href="{% url 'definitions:product_detail' product.pk %}"
                               class="btn btn-outline btn-sm" title="عرض">
                                <i class="bi bi-eye"></i>
                            </a>
                            <a href="{% url 'definitions:product_edit' product.pk %}"
                               class="btn btn-outline btn-sm" title="تعديل">
                                <i class="bi bi-pencil"></i>
                            </a>
                            <a href="{% url 'definitions:product_delete' product.pk %}"
                               class="btn btn-outline btn-sm text-danger" title="حذف"
                               onclick="return confirm('هل أنت متأكد من حذف هذا الصنف؟')">
                                <i class="bi bi-trash"></i>
                            </a>
                        </div>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="9" class="text-center py-4">
                        <div class="text-muted">
                            <i class="bi bi-inbox display-4 d-block mb-2"></i>
                            لا توجد أصناف مطابقة للبحث
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <div class="pagination-container">
            <nav>
                <ul class="pagination">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if category_id %}&category={{ category_id }}{% endif %}">
                                الأولى
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_id %}&category={{ category_id }}{% endif %}">
                                السابقة
                            </a>
                        </li>
                    {% endif %}

                    <li class="page-item active">
                        <span class="page-link">
                            {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                        </span>
                    </li>

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_id %}&category={{ category_id }}{% endif %}">
                                التالية
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_id %}&category={{ category_id }}{% endif %}">
                                الأخيرة
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
