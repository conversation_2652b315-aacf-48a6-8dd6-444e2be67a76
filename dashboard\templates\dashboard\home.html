{% extends 'base.html' %}
{% load static %}

{% block title %}لوحة التحكم الرئيسية{% endblock %}

{% block extra_css %}
<style>
    .dashboard-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 3rem 0;
        margin-bottom: 3rem;
        color: white;
        border-radius: 15px;
    }

    .dashboard-tile {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        overflow: hidden;
        margin-bottom: 1.5rem;
        border: none;
        height: 100%;
    }

    .dashboard-tile:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .dashboard-icon {
        font-size: 3rem;
        color: #667eea;
        margin-bottom: 1rem;
    }

    .stats-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        padding: 1.5rem;
        text-align: center;
        margin-bottom: 1rem;
        border: none;
    }

    .stats-number {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .stats-label {
        color: #666;
        font-size: 0.9rem;
    }

    .quick-action-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        border: none;
        height: 100%;
    }

    .quick-action-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .quick-action-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
    }

    .icon-sales { color: #28a745; }
    .icon-purchases { color: #007bff; }
    .icon-inventory { color: #ffc107; }
    .icon-accounts { color: #dc3545; }
</style>
{% endblock %}

{% block content %}
<!-- Dashboard Header -->
<div class="dashboard-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-speedometer2 me-2"></i>
                    لوحة التحكم الرئيسية
                </h1>
                <p class="mb-0 opacity-75">مرحباً بك في نظام أوساريك لإدارة الحسابات والمخزون</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="text-white">
                    <i class="bi bi-person-circle me-2"></i>
                    {{ user.get_full_name|default:user.username }}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="container-fluid">
    <div class="row g-4 mb-4">
        <div class="col-lg-2 col-md-4 col-6">
            <div class="stats-card">
                <div class="stats-number text-primary">{{ customers_count }}</div>
                <div class="stats-label">العملاء</div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-6">
            <div class="stats-card">
                <div class="stats-number text-success">{{ suppliers_count }}</div>
                <div class="stats-label">الموردين</div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-6">
            <div class="stats-card">
                <div class="stats-number text-info">{{ products_count }}</div>
                <div class="stats-label">المنتجات</div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-6">
            <div class="stats-card">
                <div class="stats-number text-warning">{{ warehouses_count }}</div>
                <div class="stats-label">المخازن</div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-6">
            <div class="stats-card">
                <div class="stats-number text-secondary">{{ currencies_count }}</div>
                <div class="stats-label">العملات</div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-6">
            <div class="stats-card">
                <div class="stats-number text-dark">{{ banks_count }}</div>
                <div class="stats-label">البنوك</div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row g-4 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="quick-action-card">
                <i class="bi bi-cart-check quick-action-icon icon-sales"></i>
                <h5 class="fw-bold">المبيعات</h5>
                <p class="text-muted small mb-3">عرض تقارير المبيعات والفواتير</p>
                <a href="#" class="btn btn-success btn-sm">عرض المبيعات</a>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="quick-action-card">
                <i class="bi bi-bag-plus quick-action-icon icon-purchases"></i>
                <h5 class="fw-bold">المشتريات</h5>
                <p class="text-muted small mb-3">إدارة المشتريات والموردين</p>
                <a href="#" class="btn btn-primary btn-sm">عرض المشتريات</a>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="quick-action-card">
                <i class="bi bi-boxes quick-action-icon icon-inventory"></i>
                <h5 class="fw-bold">المخزون</h5>
                <p class="text-muted small mb-3">إدارة المخزون والمستودعات</p>
                <a href="/warehouses/" class="btn btn-warning btn-sm">عرض المخازن</a>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="quick-action-card">
                <i class="bi bi-calculator quick-action-icon icon-accounts"></i>
                <h5 class="fw-bold">الحسابات المالية</h5>
                <p class="text-muted small mb-3">إدارة الحسابات والتقارير المالية</p>
                <a href="#" class="btn btn-danger btn-sm">عرض الحسابات</a>
            </div>
        </div>
    </div>

    <!-- Recent Activities -->
    <div class="row g-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-activity me-2"></i>النشاطات الأخيرة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <div>
                                <i class="bi bi-plus-circle text-success me-2"></i>
                                تم إضافة منتج جديد: لابتوب ديل
                            </div>
                            <small class="text-muted">منذ ساعتين</small>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <div>
                                <i class="bi bi-arrow-up-circle text-primary me-2"></i>
                                تم تحديث مخزون: ماوس لاسلكي
                            </div>
                            <small class="text-muted">منذ 4 ساعات</small>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <div>
                                <i class="bi bi-person-plus text-info me-2"></i>
                                تم إضافة عميل جديد: شركة التقنية المتقدمة
                            </div>
                            <small class="text-muted">أمس</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-exclamation-triangle me-2"></i>تنبيهات المخزون
                    </h5>
                </div>
                <div class="card-body">
                    {% if low_stock_count > 0 %}
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            يوجد {{ low_stock_count }} منتج تحت الحد الأدنى للمخزون
                        </div>
                    {% else %}
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle me-2"></i>
                            جميع المنتجات ضمن المستوى الطبيعي
                        </div>
                    {% endif %}
                    
                    <div class="d-grid">
                        <a href="/definitions/" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-gear me-1"></i>إدارة التعريفات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
