{% extends 'base.html' %}

{% block title %}حذف المخزن {{ warehouse.name }} - أوساريك{% endblock %}

{% block content %}
    <div class="page-header">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard_home' %}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{% url 'warehouses:dashboard' %}">المخازن</a></li>
                <li class="breadcrumb-item"><a href="{% url 'warehouses:warehouse_list' %}">قائمة المخازن</a></li>
                <li class="breadcrumb-item active">حذف المخزن</li>
            </ol>
        </nav>
        <h1 class="page-title text-danger">حذف المخزن</h1>
        <p class="page-subtitle">تأكيد حذف المخزن من النظام</p>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-exclamation-triangle me-2"></i>تحذير: حذف المخزن
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="warehouse-icon-lg bg-danger text-white rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3">
                            <i class="bi bi-building"></i>
                        </div>
                        <h4 class="mb-2">{{ warehouse.name }}</h4>
                        <p class="text-muted">{{ warehouse.code }}</p>
                    </div>

                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>تنبيه مهم:</strong> هذا الإجراء لا يمكن التراجع عنه!
                    </div>

                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">معلومات المخزن المراد حذفه:</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="bi bi-building me-2 text-muted"></i>
                                <strong>اسم المخزن:</strong> {{ warehouse.name }}
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-code me-2 text-muted"></i>
                                <strong>رمز المخزن:</strong> {{ warehouse.code }}
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-list me-2 text-muted"></i>
                                <strong>نوع المخزن:</strong> {{ warehouse.get_warehouse_type_display }}
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-geo-alt me-2 text-muted"></i>
                                <strong>الفرع:</strong> {{ warehouse.branch.name }}
                            </li>
                            {% if warehouse.capacity %}
                            <li class="mb-2">
                                <i class="bi bi-box me-2 text-muted"></i>
                                <strong>السعة:</strong> {{ warehouse.capacity }} متر مكعب
                            </li>
                            {% endif %}
                            {% if warehouse.manager_name %}
                            <li class="mb-2">
                                <i class="bi bi-person me-2 text-muted"></i>
                                <strong>المسؤول:</strong> {{ warehouse.manager_name }}
                            </li>
                            {% endif %}
                            {% if warehouse.phone %}
                            <li class="mb-2">
                                <i class="bi bi-telephone me-2 text-muted"></i>
                                <strong>رقم الهاتف:</strong> {{ warehouse.phone }}
                            </li>
                            {% endif %}
                            <li class="mb-2">
                                <i class="bi bi-calendar me-2 text-muted"></i>
                                <strong>تاريخ الإضافة:</strong> {{ warehouse.created_at|date:"Y/m/d H:i" }}
                            </li>
                        </ul>
                    </div>

                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> سيتم حذف جميع البيانات المرتبطة بهذا المخزن بما في ذلك:
                        <ul class="mt-2 mb-0">
                            <li>جميع المناطق والمواقع داخل المخزن</li>
                            <li>بيانات المخزون والكميات</li>
                            <li>حركات المخزون والتحويلات</li>
                            <li>أي تقارير مرتبطة بالمخزن</li>
                        </ul>
                    </div>

                    <form method="post" class="mt-4">
                        {% csrf_token %}
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'warehouses:warehouse_list' %}" class="btn btn-secondary">
                                <i class="bi bi-arrow-right me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-danger" id="deleteBtn">
                                <i class="bi bi-trash me-2"></i>تأكيد الحذف
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Alternative Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-lightbulb me-2"></i>بدائل أخرى
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">بدلاً من حذف المخزن، يمكنك:</p>
                    <div class="d-grid gap-2">
                        <a href="{% url 'warehouses:warehouse_edit' warehouse.pk %}" class="btn btn-outline-primary">
                            <i class="bi bi-pencil me-2"></i>تعديل بيانات المخزن
                        </a>
                        {% if warehouse.is_active %}
                            <button class="btn btn-outline-warning" onclick="deactivateWarehouse()">
                                <i class="bi bi-pause-circle me-2"></i>إلغاء تفعيل المخزن بدلاً من الحذف
                            </button>
                        {% endif %}
                        <a href="{% url 'warehouses:warehouse_detail' warehouse.pk %}" class="btn btn-outline-info">
                            <i class="bi bi-eye me-2"></i>عرض تفاصيل المخزن
                        </a>
                        <a href="{% url 'warehouses:warehouse_list' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-list me-2"></i>العودة إلى قائمة المخازن
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_css %}
<style>
    .warehouse-icon-lg {
        width: 80px;
        height: 80px;
        font-size: 2rem;
        font-weight: 700;
    }
    
    .breadcrumb {
        background-color: transparent;
        padding: 0;
        margin-bottom: 1rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        content: "←";
    }
    
    .card-header {
        border-bottom: 1px solid #dee2e6;
    }
    
    .list-unstyled li {
        padding: 0.25rem 0;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const deleteBtn = document.getElementById('deleteBtn');
        const form = document.querySelector('form');
        
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show confirmation dialog
            if (confirm('هل أنت متأكد تماماً من حذف هذا المخزن؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                deleteBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري الحذف...';
                deleteBtn.disabled = true;
                
                // Submit the form
                form.submit();
            }
        });
    });
    
    function deactivateWarehouse() {
        if (confirm('هل تريد إلغاء تفعيل المخزن بدلاً من حذفه؟')) {
            // Redirect to edit page with deactivation
            window.location.href = '{% url "warehouses:warehouse_edit" warehouse.pk %}?deactivate=1';
        }
    }
</script>
{% endblock %}
