{% extends 'base.html' %}
{% load static %}

{% block title %}لوحة تحكم المخازن{% endblock %}

{% block extra_css %}
<style>
    .warehouse-header {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        padding: 3rem 0;
        margin-bottom: 3rem;
        color: white;
        border-radius: 15px;
    }

    .stat-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        padding: 2rem;
        text-align: center;
        margin-bottom: 1.5rem;
        border: none;
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #666;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .action-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        border: none;
        height: 100%;
    }

    .action-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .action-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
    }

    .icon-add { color: #28a745; }
    .icon-remove { color: #dc3545; }
    .icon-transfer { color: #ffc107; }
    .icon-report { color: #6f42c1; }

    .warehouse-summary {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .warehouse-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        overflow: hidden;
        margin-bottom: 1.5rem;
        border: none;
        height: 100%;
    }

    .warehouse-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
</style>
{% endblock %}

{% block content %}
<!-- Warehouse Header -->
<div class="warehouse-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-building me-2"></i>
                    لوحة تحكم المخازن
                </h1>
                <p class="mb-0 opacity-75">إدارة شاملة للمخازن والمخزون والحركات</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="btn-group">
                    <a href="{% url 'warehouses:add_stock' %}" class="btn btn-light">
                        <i class="bi bi-plus-circle me-1"></i>إضافة مخزون
                    </a>
                    <a href="{% url 'warehouses:remove_stock' %}" class="btn btn-outline-light">
                        <i class="bi bi-dash-circle me-1"></i>صرف مخزون
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics -->
<div class="container-fluid">
    <div class="row g-4 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="stat-card">
                <div class="stat-number text-primary">{{ total_warehouses|default:0 }}</div>
                <div class="stat-label">إجمالي المخازن</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stat-card">
                <div class="stat-number text-success">{{ total_products|default:0 }}</div>
                <div class="stat-label">الأصناف المتوفرة</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stat-card">
                <div class="stat-number text-info">{{ total_transactions_today|default:0 }}</div>
                <div class="stat-label">حركات اليوم</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stat-card">
                <div class="stat-number text-warning">{{ total_inventory_value|floatformat:0|default:0 }} ج.م</div>
                <div class="stat-label">قيمة المخزون</div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row g-4 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="action-card">
                <i class="bi bi-plus-circle action-icon icon-add"></i>
                <h5 class="fw-bold">إضافة مخزون</h5>
                <p class="text-muted small mb-3">إذن إدخال - زيادة المخزون</p>
                <a href="{% url 'warehouses:add_stock' %}" class="btn btn-success btn-sm">إضافة مخزون</a>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="action-card">
                <i class="bi bi-dash-circle action-icon icon-remove"></i>
                <h5 class="fw-bold">صرف مخزون</h5>
                <p class="text-muted small mb-3">إذن إخراج - تقليل المخزون</p>
                <a href="{% url 'warehouses:remove_stock' %}" class="btn btn-danger btn-sm">صرف مخزون</a>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="action-card">
                <i class="bi bi-arrow-left-right action-icon icon-transfer"></i>
                <h5 class="fw-bold">نقل بين المخازن</h5>
                <p class="text-muted small mb-3">نقل الأصناف بين المخازن</p>
                <a href="{% url 'warehouses:transfer_stock' %}" class="btn btn-warning btn-sm">نقل مخزون</a>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="action-card">
                <i class="bi bi-graph-up action-icon icon-report"></i>
                <h5 class="fw-bold">التقارير</h5>
                <p class="text-muted small mb-3">تقارير المخزون والحركات</p>
                <a href="{% url 'warehouses:inventory_list' %}" class="btn btn-primary btn-sm">عرض التقارير</a>
            </div>
        </div>
    </div>

    <!-- Warehouses Summary -->
    <div class="row g-4">
        <div class="col-12">
            <div class="warehouse-summary">
                <h5 class="mb-3">
                    <i class="bi bi-building me-2"></i>
                    ملخص المخازن
                </h5>
                <div class="row">
                    {% for data in warehouses_data %}
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="warehouse-card">
                            <div class="card-body">
                                <h6 class="card-title">{{ data.warehouse.name }}</h6>
                                <p class="card-text text-muted small">{{ data.warehouse.description|default:"لا يوجد وصف" }}</p>
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="fw-bold text-primary">{{ data.products_count }}</div>
                                        <small class="text-muted">صنف</small>
                                    </div>
                                    <div class="col-6">
                                        <div class="fw-bold text-success">{{ data.total_value|floatformat:0 }}</div>
                                        <small class="text-muted">ج.م</small>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <a href="{% url 'warehouses:warehouse_report' data.warehouse.id %}" class="btn btn-outline-primary btn-sm">
                                        عرض التفاصيل
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="col-12 text-center py-4">
                        <i class="bi bi-building" style="font-size: 3rem; color: #ddd;"></i>
                        <h5 class="mt-2 text-muted">لا توجد مخازن</h5>
                        <p class="text-muted">قم بإضافة مخازن من قسم التعريفات</p>
                        <a href="{% url 'definitions:dashboard' %}" class="btn btn-primary">
                            إضافة مخازن
                        </a>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}