"""
URL configuration for osaric project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.urls import path, include
from django.contrib.auth import views as auth_views

urlpatterns = [
    path('', include('dashboard.urls')),
    path('accounts/login/', auth_views.LoginView.as_view(), name='login'),
    path('accounts/logout/', auth_views.LogoutView.as_view(), name='logout'),
    path('customers/', include('customers.urls', namespace='customers')),
    path('definitions/', include('definitions.urls', namespace='definitions')),
    path('warehouses/', include('warehouses.urls', namespace='warehouses')),
    path('manufacturing/', include('manufacturing.urls', namespace='manufacturing')),
    path('sales/', include('sales.urls', namespace='sales')),
    path('purchases/', include('purchases.urls', namespace='purchases')),
    path('assets/', include('assets.urls', namespace='assets')),
    path('banks/', include('banks.urls', namespace='banks')),
    path('treasuries/', include('treasuries.urls', namespace='treasuries')),
    path('accounting/', include('accounting.urls', namespace='accounting')),
    path('branches/', include('branches.urls', namespace='branches')),
    path('hr/', include('hr.urls', namespace='hr')),
    path('reports/', include('reports.urls', namespace='reports')),
    path('settings/', include('settings.urls', namespace='settings')),
]
