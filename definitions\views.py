from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q, Count, F
from django.db import models
from django.core.paginator import Paginator
from django.http import JsonResponse
from .models import (
    WarehouseDefinition, ProductDefinition, ProductCategory,
    CurrencyDefinition, BankDefinition, CashBoxDefinition,
    PersonDefinition, WarehouseLocation, AssetGroup, AssetBrand,
    ExpenseType, ExpenseName, RevenueType, RevenueName, ProfitCenter
)

@login_required
def definitions_dashboard(request):
    """لوحة تحكم التعريفات"""
    # إحصائيات التعريفات
    from .models import ProductCode
    stats = {
        'warehouses': WarehouseDefinition.objects.count(),
        'warehouse_locations': WarehouseLocation.objects.count(),
        'products': ProductDefinition.objects.count(),
        'categories': ProductCategory.objects.count(),
        'product_codes': ProductCode.objects.count(),
        'persons': PersonDefinition.objects.count(),
        'customers': PersonDefinition.objects.filter(person_type='customer').count(),
        'suppliers': PersonDefinition.objects.filter(person_type='supplier').count(),
        'employees': PersonDefinition.objects.filter(person_type='employee').count(),
        'currencies': CurrencyDefinition.objects.count(),
        'banks': BankDefinition.objects.count(),
        'cashboxes': CashBoxDefinition.objects.count(),
        'asset_groups': AssetGroup.objects.count(),
        'asset_brands': AssetBrand.objects.count(),
        'expense_types': ExpenseType.objects.count(),
        'expense_names': ExpenseName.objects.count(),
        'revenue_types': RevenueType.objects.count(),
        'revenue_names': RevenueName.objects.count(),
        'profit_centers': ProfitCenter.objects.count(),
    }

    # إحصائيات إضافية
    recent_definitions = {
        'recent_products': ProductDefinition.objects.filter(is_active=True).order_by('-created_at')[:5],
        'recent_persons': PersonDefinition.objects.filter(is_active=True).order_by('-created_at')[:5],
        'recent_warehouses': WarehouseDefinition.objects.filter(is_active=True).order_by('-created_at')[:5],
    }

    context = {
        'stats': stats,
        'recent_definitions': recent_definitions,
    }

    return render(request, 'definitions/dashboard.html', context)

# ===== أماكن الأصناف في المخازن =====

@login_required
def warehouse_location_list(request):
    """قائمة أماكن الأصناف في المخازن"""
    from .forms import WarehouseLocationForm

    # الحصول على جميع الأماكن
    locations = WarehouseLocation.objects.select_related('warehouse', 'created_by').all()

    # تطبيق الفلاتر
    warehouse_filter = request.GET.get('warehouse')
    status_filter = request.GET.get('status')
    search_query = request.GET.get('search')

    if warehouse_filter:
        locations = locations.filter(warehouse_id=warehouse_filter)

    if status_filter == 'active':
        locations = locations.filter(is_active=True)
    elif status_filter == 'inactive':
        locations = locations.filter(is_active=False)

    if search_query:
        locations = locations.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    # ترتيب النتائج
    locations = locations.order_by('warehouse__name', 'code')

    # التصفح بالصفحات
    paginator = Paginator(locations, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # إحصائيات
    total_locations = WarehouseLocation.objects.count()
    active_locations = WarehouseLocation.objects.filter(is_active=True).count()
    full_locations = WarehouseLocation.objects.filter(
        max_capacity__isnull=False,
        current_capacity__gte=models.F('max_capacity')
    ).count()
    total_warehouses = WarehouseDefinition.objects.filter(is_active=True).count()

    # قائمة المخازن للفلتر
    warehouses = WarehouseDefinition.objects.filter(is_active=True).order_by('name')

    context = {
        'locations': page_obj,
        'warehouses': warehouses,
        'total_locations': total_locations,
        'active_locations': active_locations,
        'full_locations': full_locations,
        'total_warehouses': total_warehouses,
        'is_paginated': page_obj.has_other_pages(),
        'page_obj': page_obj,
    }

    return render(request, 'definitions/warehouse_location_list.html', context)

@login_required
def warehouse_location_create(request):
    """إضافة مكان جديد"""
    from .forms import WarehouseLocationForm

    import sys
    sys.stdout.write(f"DEBUG: warehouse_location_create called - Method: {request.method}\n")
    sys.stdout.flush()

    if request.method == 'POST':
        sys.stdout.write(f"DEBUG: POST request received\n")
        sys.stdout.write(f"DEBUG: POST data: {dict(request.POST)}\n")
        sys.stdout.flush()

        form = WarehouseLocationForm(request.POST)
        sys.stdout.write(f"DEBUG: Form created\n")
        sys.stdout.flush()

        if form.is_valid():
            sys.stdout.write(f"DEBUG: Form is valid\n")
            sys.stdout.flush()
            try:
                location = form.save(commit=False)
                location.created_by = request.user
                location.save()
                sys.stdout.write(f"DEBUG: Location saved successfully: {location.name}\n")
                sys.stdout.flush()
                messages.success(request, f'تم إضافة المكان "{location.name}" بنجاح')
                return redirect('definitions:warehouse_location_list')
            except Exception as e:
                sys.stdout.write(f"DEBUG: Error saving location: {e}\n")
                sys.stdout.flush()
                import traceback
                traceback.print_exc()
                messages.error(request, f'حدث خطأ أثناء حفظ المكان: {e}')
        else:
            sys.stdout.write(f"DEBUG: Form is not valid\n")
            sys.stdout.write(f"DEBUG: Form errors: {form.errors}\n")
            sys.stdout.write(f"DEBUG: Form non_field_errors: {form.non_field_errors()}\n")
            sys.stdout.flush()
            for field, errors in form.errors.items():
                sys.stdout.write(f"DEBUG: Field '{field}' errors: {errors}\n")
                sys.stdout.flush()

            # إضافة رسائل خطأ للمستخدم
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'خطأ في حقل {field}: {error}')

            if form.non_field_errors():
                for error in form.non_field_errors():
                    messages.error(request, f'خطأ عام: {error}')
    else:
        sys.stdout.write(f"DEBUG: GET request - creating new form\n")
        sys.stdout.flush()
        form = WarehouseLocationForm()

    context = {
        'form': form,
        'title': 'إضافة مكان جديد'
    }

    return render(request, 'definitions/warehouse_location_form.html', context)

@login_required
def warehouse_location_detail(request, location_id):
    """عرض تفاصيل المكان"""
    location = get_object_or_404(WarehouseLocation, id=location_id)

    context = {
        'object': location,
    }

    return render(request, 'definitions/warehouse_location_detail.html', context)

@login_required
def warehouse_location_edit(request, location_id):
    """تعديل المكان"""
    from .forms import WarehouseLocationForm

    location = get_object_or_404(WarehouseLocation, id=location_id)

    if request.method == 'POST':
        form = WarehouseLocationForm(request.POST, instance=location)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث المكان "{location.name}" بنجاح')
            return redirect('definitions:warehouse_location_detail', location.id)
    else:
        form = WarehouseLocationForm(instance=location)

    context = {
        'form': form,
        'object': location,
        'title': f'تعديل المكان: {location.name}'
    }

    return render(request, 'definitions/warehouse_location_form.html', context)

@login_required
def warehouse_location_delete(request, location_id):
    """حذف المكان مباشر مع تأكيد JavaScript فقط"""
    if request.method == 'POST':
        try:
            location = get_object_or_404(WarehouseLocation, id=location_id)
            location_name = location.name

            # حذف المكان مباشرة
            location.delete()

            # رسالة نجاح
            messages.success(request, f'تم حذف المكان "{location_name}" بنجاح!')

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء حذف المكان: {e}')

    # العودة لقائمة الأماكن في جميع الحالات
    return redirect('definitions:warehouse_location_list')

# ===== إدارة الأصناف =====



@login_required
def product_create(request):
    """إضافة صنف جديد"""
    from .forms import ProductDefinitionForm

    if request.method == 'POST':
        print("تم استلام POST request")  # للتشخيص
        form = ProductDefinitionForm(request.POST, request.FILES)
        print(f"بيانات النموذج: {request.POST}")  # للتشخيص

        if form.is_valid():
            print("النموذج صحيح")  # للتشخيص
            try:
                product = form.save(commit=False)
                product.created_by = request.user

                # إصلاح مشكلة الباركود الفارغ
                if not product.barcode:
                    product.barcode = None

                product.save()
                print(f"تم حفظ الصنف: {product.name}")  # للتشخيص
                messages.success(request, f'تم إضافة الصنف "{product.name}" بنجاح')
                return redirect('definitions:product_detail', product.id)
            except Exception as e:
                print(f"خطأ في الحفظ: {e}")  # للتشخيص
                messages.error(request, f'حدث خطأ أثناء حفظ الصنف: {e}')
        else:
            print(f"أخطاء النموذج: {form.errors}")  # للتشخيص
            messages.error(request, 'يرجى تصحيح الأخطاء في النموذج')
    else:
        form = ProductDefinitionForm()

    context = {
        'form': form,
        'title': 'إضافة صنف جديد'
    }

    return render(request, 'definitions/product_form.html', context)

@login_required
def product_detail(request, product_id):
    """عرض تفاصيل الصنف"""
    product = get_object_or_404(ProductDefinition, id=product_id)

    context = {
        'object': product,
    }

    return render(request, 'definitions/product_detail.html', context)

@login_required
def product_edit(request, product_id):
    """تعديل الصنف"""
    from .forms import ProductDefinitionForm

    product = get_object_or_404(ProductDefinition, id=product_id)

    if request.method == 'POST':
        form = ProductDefinitionForm(request.POST, request.FILES, instance=product)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث الصنف "{product.name}" بنجاح')
            return redirect('definitions:product_detail', product.id)
    else:
        form = ProductDefinitionForm(instance=product)

    context = {
        'form': form,
        'object': product,
        'title': f'تعديل الصنف: {product.name}'
    }

    return render(request, 'definitions/product_form.html', context)

@login_required
def product_delete(request, product_id):
    """حذف الصنف"""
    try:
        product = get_object_or_404(ProductDefinition, id=product_id)
        print(f"DEBUG: Found product: {product.name}")  # للتشخيص

        if request.method == 'POST':
            print(f"DEBUG: POST request received for product: {product.name}")  # للتشخيص
            try:
                product_name = product.name
                product.delete()
                print(f"DEBUG: Product deleted successfully: {product_name}")  # للتشخيص
                messages.success(request, f'تم حذف الصنف "{product_name}" بنجاح')
                return redirect('definitions:product_list')
            except Exception as e:
                print(f"DEBUG: Error deleting product: {e}")  # للتشخيص
                messages.error(request, f'حدث خطأ أثناء حذف الصنف: {e}')
                return redirect('definitions:product_list')

        print(f"DEBUG: Rendering delete confirmation page")  # للتشخيص
        context = {
            'object': product,
            'title': f'حذف الصنف: {product.name}'
        }

        return render(request, 'definitions/product_confirm_delete.html', context)

    except Exception as e:
        print(f"DEBUG: Error in product_delete view: {e}")  # للتشخيص
        import traceback
        traceback.print_exc()
        from django.http import HttpResponse
        return HttpResponse(f"خطأ في عرض صفحة الحذف: {e}", status=500)

@login_required
def product_duplicate(request, product_id):
    """نسخ الصنف"""
    from .forms import ProductDefinitionForm

    original_product = get_object_or_404(ProductDefinition, id=product_id)

    if request.method == 'POST':
        form = ProductDefinitionForm(request.POST, request.FILES)
        if form.is_valid():
            product = form.save(commit=False)
            product.created_by = request.user
            product.save()
            messages.success(request, f'تم نسخ الصنف "{product.name}" بنجاح')
            return redirect('definitions:product_detail', product.id)
    else:
        # إنشاء نسخة من الصنف الأصلي
        form_data = {
            'code': f"{original_product.code}_copy",
            'name': f"{original_product.name} (نسخة)",
            'description': original_product.description,
            'category': original_product.category,
            'cost_price': original_product.cost_price,
            'selling_price': original_product.selling_price,
            'wholesale_price': original_product.wholesale_price,
            'unit': original_product.unit,
            'current_stock': 0,  # البدء بمخزون صفر للنسخة
            'minimum_stock': original_product.minimum_stock,
            'maximum_stock': original_product.maximum_stock,
            'is_active': original_product.is_active,
            'track_stock': original_product.track_stock,
        }
        form = ProductDefinitionForm(initial=form_data)

    context = {
        'form': form,
        'original_product': original_product,
        'title': f'نسخ الصنف: {original_product.name}'
    }

    return render(request, 'definitions/product_form.html', context)

@login_required
def warehouse_list(request):
    """قائمة تعريف المخازن"""
    search_query = request.GET.get('search', '')
    warehouse_type = request.GET.get('type', '')

    warehouses = WarehouseDefinition.objects.all()

    if search_query:
        warehouses = warehouses.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(manager_name__icontains=search_query) |
            Q(address__icontains=search_query)
        )

    if warehouse_type:
        warehouses = warehouses.filter(warehouse_type=warehouse_type)

    warehouses = warehouses.order_by('-created_at')

    # Pagination
    paginator = Paginator(warehouses, 20)  # 20 مخزن في الصفحة للجدول
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # إحصائيات
    total_warehouses = WarehouseDefinition.objects.count()

    # أنواع المخازن للفلترة
    warehouse_types = WarehouseDefinition.WAREHOUSE_TYPES

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'warehouse_type': warehouse_type,
        'warehouse_types': warehouse_types,
        'total_warehouses': total_warehouses,
    }

    return render(request, 'definitions/warehouse_list.html', context)

@login_required
def warehouse_create(request):
    """إنشاء مخزن جديد"""
    if request.method == 'POST':
        # استخراج البيانات من النموذج
        code = request.POST.get('code')
        name = request.POST.get('name')
        warehouse_type = request.POST.get('warehouse_type')
        address = request.POST.get('address', '')
        phone = request.POST.get('phone', '')
        manager_name = request.POST.get('manager_name', '')
        description = request.POST.get('description', '')
        is_active = request.POST.get('is_active') == 'on'
        allow_negative_stock = request.POST.get('allow_negative_stock') == 'on'
        auto_reorder = request.POST.get('auto_reorder') == 'on'

        # التحقق من عدم تكرار الكود
        if WarehouseDefinition.objects.filter(code=code).exists():
            messages.error(request, f'كود المخزن "{code}" موجود بالفعل. يرجى استخدام كود آخر.')
            return render(request, 'definitions/warehouse_form.html', {
                'warehouse_types': WarehouseDefinition.WAREHOUSE_TYPES,
                'form_data': request.POST
            })

        # إنشاء المخزن
        warehouse = WarehouseDefinition.objects.create(
            code=code,
            name=name,
            warehouse_type=warehouse_type,
            address=address,
            phone=phone,
            manager_name=manager_name,
            description=description,
            is_active=is_active,
            allow_negative_stock=allow_negative_stock,
            auto_reorder=auto_reorder,
            created_by=request.user
        )

        messages.success(request, f'تم إنشاء المخزن "{warehouse.name}" بنجاح!')
        return redirect('definitions:warehouse_list')

    context = {
        'warehouse_types': WarehouseDefinition.WAREHOUSE_TYPES,
        'action': 'create'
    }

    return render(request, 'definitions/warehouse_form.html', context)

@login_required
def warehouse_edit(request, warehouse_id):
    """تعديل مخزن موجود"""
    warehouse = get_object_or_404(WarehouseDefinition, id=warehouse_id)

    if request.method == 'POST':
        # استخراج البيانات من النموذج
        code = request.POST.get('code')
        name = request.POST.get('name')
        warehouse_type = request.POST.get('warehouse_type')
        address = request.POST.get('address', '')
        phone = request.POST.get('phone', '')
        manager_name = request.POST.get('manager_name', '')
        description = request.POST.get('description', '')
        is_active = request.POST.get('is_active') == 'on'
        allow_negative_stock = request.POST.get('allow_negative_stock') == 'on'
        auto_reorder = request.POST.get('auto_reorder') == 'on'

        # التحقق من عدم تكرار الكود (باستثناء المخزن الحالي)
        if WarehouseDefinition.objects.filter(code=code).exclude(id=warehouse.id).exists():
            messages.error(request, f'كود المخزن "{code}" موجود بالفعل. يرجى استخدام كود آخر.')
            return render(request, 'definitions/warehouse_form.html', {
                'warehouse': warehouse,
                'warehouse_types': WarehouseDefinition.WAREHOUSE_TYPES,
                'action': 'edit',
                'form_data': request.POST
            })

        # تحديث المخزن
        warehouse.code = code
        warehouse.name = name
        warehouse.warehouse_type = warehouse_type
        warehouse.address = address
        warehouse.phone = phone
        warehouse.manager_name = manager_name
        warehouse.description = description
        warehouse.is_active = is_active
        warehouse.allow_negative_stock = allow_negative_stock
        warehouse.auto_reorder = auto_reorder
        warehouse.save()

        messages.success(request, f'تم تحديث المخزن "{warehouse.name}" بنجاح!')
        return redirect('definitions:warehouse_list')

    context = {
        'warehouse': warehouse,
        'warehouse_types': WarehouseDefinition.WAREHOUSE_TYPES,
        'action': 'edit'
    }

    return render(request, 'definitions/warehouse_form.html', context)

@login_required
def warehouse_detail(request, warehouse_id):
    """تفاصيل المخزن"""
    warehouse = get_object_or_404(WarehouseDefinition, id=warehouse_id)

    context = {
        'warehouse': warehouse,
    }

    return render(request, 'definitions/warehouse_detail.html', context)

@login_required
def warehouse_delete(request, warehouse_id):
    """حذف مخزن مباشر مع تأكيد JavaScript فقط"""
    print(f"DEBUG: warehouse_delete called with ID: {warehouse_id}")
    print(f"DEBUG: Request method: {request.method}")
    print(f"DEBUG: User: {request.user}")

    if request.method == 'POST':
        print(f"DEBUG: POST request received")
        try:
            warehouse = get_object_or_404(WarehouseDefinition, id=warehouse_id)
            warehouse_name = warehouse.name
            print(f"DEBUG: Found warehouse: {warehouse_name}")

            # حذف المخزن مباشرة
            warehouse.delete()
            print(f"DEBUG: Warehouse deleted successfully")

            # رسالة نجاح
            messages.success(request, f'تم حذف المخزن "{warehouse_name}" بنجاح!')
            print(f"DEBUG: Success message added")

        except Exception as e:
            print(f"DEBUG: Error occurred: {e}")
            import traceback
            traceback.print_exc()
            messages.error(request, f'حدث خطأ أثناء حذف المخزن: {e}')
    else:
        print(f"DEBUG: Non-POST request received")

    # العودة لقائمة المخازن في جميع الحالات
    print(f"DEBUG: Redirecting to warehouse_list")
    return redirect('definitions:warehouse_list')

@login_required
def warehouse_quick_delete(request, warehouse_id):
    """حذف سريع للمخزن من الجدول مباشرة"""
    if request.method == 'POST':
        try:
            warehouse = get_object_or_404(WarehouseDefinition, id=warehouse_id)
            print(f"DEBUG: Quick delete for warehouse: {warehouse.name}")  # للتشخيص

            # تم تعطيل فحص البيانات المرتبطة مؤقتاً لأن نماذج warehouses معطلة
            warehouse_name = warehouse.name
            warehouse.delete()
            print(f"DEBUG: Quick delete successful: {warehouse_name}")  # للتشخيص
            messages.success(request, f'تم حذف المخزن "{warehouse_name}" بنجاح!')

        except Exception as e:
            print(f"DEBUG: Error in quick delete: {e}")  # للتشخيص
            messages.error(request, f'حدث خطأ أثناء حذف المخزن: {e}')

        return redirect('definitions:warehouse_list')

    # إذا لم يكن POST، إعادة توجيه للقائمة
    return redirect('definitions:warehouse_list')

# ===== تعريف العملات =====
@login_required
def currency_list(request):
    """قائمة تعريف العملات"""
    search_query = request.GET.get('search', '')

    currencies = CurrencyDefinition.objects.all()

    if search_query:
        currencies = currencies.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(symbol__icontains=search_query)
        )

    currencies = currencies.order_by('-is_base_currency', 'code')

    paginator = Paginator(currencies, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'total_currencies': CurrencyDefinition.objects.count(),
    }

    return render(request, 'definitions/currency_list.html', context)

@login_required
def currency_create(request):
    """إنشاء عملة جديدة"""
    if request.method == 'POST':
        code = request.POST.get('code')
        name = request.POST.get('name')
        name_en = request.POST.get('name_en', '')
        symbol = request.POST.get('symbol')
        exchange_rate = request.POST.get('exchange_rate', '1.00')
        is_base_currency = request.POST.get('is_base_currency') == 'on'
        is_active = request.POST.get('is_active') == 'on'

        # التحقق من عدم تكرار الكود
        if CurrencyDefinition.objects.filter(code=code).exists():
            messages.error(request, f'كود العملة "{code}" موجود بالفعل.')
            return render(request, 'definitions/currency_form.html', {'form_data': request.POST})

        # إذا كانت عملة أساسية، إلغاء العملة الأساسية السابقة
        if is_base_currency:
            CurrencyDefinition.objects.filter(is_base_currency=True).update(is_base_currency=False)

        currency = CurrencyDefinition.objects.create(
            code=code,
            name=name,
            name_en=name_en,
            symbol=symbol,
            exchange_rate=exchange_rate,
            is_base_currency=is_base_currency,
            is_active=is_active,
            created_by=request.user
        )

        messages.success(request, f'تم إنشاء العملة "{currency.name}" بنجاح!')
        return redirect('definitions:currency_list')

    return render(request, 'definitions/currency_form.html', {'action': 'create'})

@login_required
def currency_edit(request, currency_id):
    """تعديل عملة موجودة"""
    currency = get_object_or_404(CurrencyDefinition, id=currency_id)

    if request.method == 'POST':
        code = request.POST.get('code')
        name = request.POST.get('name')
        name_en = request.POST.get('name_en', '')
        symbol = request.POST.get('symbol')
        exchange_rate = request.POST.get('exchange_rate', '1.00')
        is_base_currency = request.POST.get('is_base_currency') == 'on'
        is_active = request.POST.get('is_active') == 'on'

        # التحقق من عدم تكرار الكود
        if CurrencyDefinition.objects.filter(code=code).exclude(id=currency.id).exists():
            messages.error(request, f'كود العملة "{code}" موجود بالفعل.')
            return render(request, 'definitions/currency_form.html', {
                'currency': currency, 'action': 'edit', 'form_data': request.POST
            })

        # إذا كانت عملة أساسية، إلغاء العملة الأساسية السابقة
        if is_base_currency and not currency.is_base_currency:
            CurrencyDefinition.objects.filter(is_base_currency=True).update(is_base_currency=False)

        currency.code = code
        currency.name = name
        currency.name_en = name_en
        currency.symbol = symbol
        currency.exchange_rate = exchange_rate
        currency.is_base_currency = is_base_currency
        currency.is_active = is_active
        currency.save()

        messages.success(request, f'تم تحديث العملة "{currency.name}" بنجاح!')
        return redirect('definitions:currency_list')

    context = {'currency': currency, 'action': 'edit'}
    return render(request, 'definitions/currency_form.html', context)

@login_required
def currency_detail(request, currency_id):
    """تفاصيل العملة"""
    currency = get_object_or_404(CurrencyDefinition, id=currency_id)
    return render(request, 'definitions/currency_detail.html', {'currency': currency})

@login_required
def currency_delete(request, currency_id):
    """حذف عملة"""
    currency = get_object_or_404(CurrencyDefinition, id=currency_id)

    if request.method == 'POST':
        # منع حذف العملة الأساسية
        if currency.is_base_currency:
            messages.error(request, 'لا يمكن حذف العملة الأساسية!')
            return redirect('definitions:currency_detail', currency_id=currency.id)

        currency_name = currency.name
        currency.delete()
        messages.success(request, f'تم حذف العملة "{currency_name}" بنجاح!')
        return redirect('definitions:currency_list')

    return render(request, 'definitions/currency_confirm_delete.html', {'currency': currency})

@login_required
def currency_quick_delete(request, currency_id):
    """حذف سريع للعملة"""
    if request.method == 'POST':
        currency = get_object_or_404(CurrencyDefinition, id=currency_id)

        if currency.is_base_currency:
            messages.error(request, f'لا يمكن حذف العملة الأساسية "{currency.name}"!')
        else:
            currency_name = currency.name
            currency.delete()
            messages.success(request, f'تم حذف العملة "{currency_name}" بنجاح!')

        return redirect('definitions:currency_list')

    return redirect('definitions:currency_list')

# ===== تعريف البنوك =====
@login_required
def bank_list(request):
    """قائمة تعريف البنوك"""
    search_query = request.GET.get('search', '')

    banks = BankDefinition.objects.all()

    if search_query:
        banks = banks.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(swift_code__icontains=search_query)
        )

    banks = banks.order_by('code')

    paginator = Paginator(banks, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'total_banks': BankDefinition.objects.count(),
    }

    return render(request, 'definitions/bank_list.html', context)

@login_required
def bank_create(request):
    """إنشاء بنك جديد"""
    if request.method == 'POST':
        code = request.POST.get('code')
        name = request.POST.get('name')
        name_en = request.POST.get('name_en', '')
        swift_code = request.POST.get('swift_code', '')
        address = request.POST.get('address', '')
        phone = request.POST.get('phone', '')
        is_active = request.POST.get('is_active') == 'on'

        if BankDefinition.objects.filter(code=code).exists():
            messages.error(request, f'كود البنك "{code}" موجود بالفعل.')
            return render(request, 'definitions/bank_form.html', {'form_data': request.POST})

        bank = BankDefinition.objects.create(
            code=code,
            name=name,
            name_en=name_en,
            swift_code=swift_code,
            address=address,
            phone=phone,
            is_active=is_active,
            created_by=request.user
        )

        messages.success(request, f'تم إنشاء البنك "{bank.name}" بنجاح!')
        return redirect('definitions:bank_list')

    return render(request, 'definitions/bank_form.html', {'action': 'create'})

@login_required
def bank_edit(request, bank_id):
    """تعديل بنك موجود"""
    bank = get_object_or_404(BankDefinition, id=bank_id)

    if request.method == 'POST':
        code = request.POST.get('code')
        name = request.POST.get('name')
        name_en = request.POST.get('name_en', '')
        swift_code = request.POST.get('swift_code', '')
        address = request.POST.get('address', '')
        phone = request.POST.get('phone', '')
        is_active = request.POST.get('is_active') == 'on'

        if BankDefinition.objects.filter(code=code).exclude(id=bank.id).exists():
            messages.error(request, f'كود البنك "{code}" موجود بالفعل.')
            return render(request, 'definitions/bank_form.html', {
                'bank': bank, 'action': 'edit', 'form_data': request.POST
            })

        bank.code = code
        bank.name = name
        bank.name_en = name_en
        bank.swift_code = swift_code
        bank.address = address
        bank.phone = phone
        bank.is_active = is_active
        bank.save()

        messages.success(request, f'تم تحديث البنك "{bank.name}" بنجاح!')
        return redirect('definitions:bank_list')

    context = {'bank': bank, 'action': 'edit'}
    return render(request, 'definitions/bank_form.html', context)

@login_required
def bank_detail(request, bank_id):
    """تفاصيل البنك"""
    bank = get_object_or_404(BankDefinition, id=bank_id)
    return render(request, 'definitions/bank_detail.html', {'bank': bank})

@login_required
def bank_delete(request, bank_id):
    """حذف بنك"""
    bank = get_object_or_404(BankDefinition, id=bank_id)

    if request.method == 'POST':
        bank_name = bank.name
        bank.delete()
        messages.success(request, f'تم حذف البنك "{bank_name}" بنجاح!')
        return redirect('definitions:bank_list')

    return render(request, 'definitions/bank_confirm_delete.html', {'bank': bank})

@login_required
def bank_quick_delete(request, bank_id):
    """حذف سريع للبنك"""
    if request.method == 'POST':
        bank = get_object_or_404(BankDefinition, id=bank_id)
        bank_name = bank.name
        bank.delete()
        messages.success(request, f'تم حذف البنك "{bank_name}" بنجاح!')
        return redirect('definitions:bank_list')

    return redirect('definitions:bank_list')

# ===== تعريف الأصناف =====
@login_required
def product_list(request):
    """قائمة تعريف الأصناف"""
    search_query = request.GET.get('search', '')
    category_id = request.GET.get('category', '')

    products = ProductDefinition.objects.all()

    if search_query:
        products = products.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(barcode__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    if category_id:
        products = products.filter(category_id=category_id)

    products = products.select_related('category').order_by('code')

    paginator = Paginator(products, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # الحصول على الفئات للفلترة
    categories = ProductCategory.objects.filter(is_active=True).order_by('name')

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'category_id': category_id,
        'categories': categories,
        'total_products': ProductDefinition.objects.count(),
        'active_products': ProductDefinition.objects.filter(is_active=True).count(),
        'low_stock_products': ProductDefinition.objects.filter(minimum_stock__gt=0).count(),
        'out_of_stock_products': 0,  # مؤقتاً حتى نضيف نظام المخزون
    }

    return render(request, 'definitions/product_list.html', context)

# تم حذف الدالة المكررة - استخدم الدالة الأولى التي تستخدم Django forms

@login_required
def product_edit(request, product_id):
    """تعديل صنف موجود"""
    product = get_object_or_404(ProductDefinition, id=product_id)

    if request.method == 'POST':
        code = request.POST.get('code')
        name = request.POST.get('name')
        name_en = request.POST.get('name_en', '')
        barcode = request.POST.get('barcode', '')
        category_id = request.POST.get('category')
        main_unit = request.POST.get('unit')
        product_type = request.POST.get('product_type', 'product')
        description = request.POST.get('description', '')
        cost_price = request.POST.get('cost_price', '0')
        selling_price = request.POST.get('selling_price', '0')
        minimum_stock = request.POST.get('min_stock', '0')
        maximum_stock = request.POST.get('max_stock', '0')
        is_active = request.POST.get('is_active') == 'on'
        track_inventory = request.POST.get('track_inventory') == 'on'

        # التحقق من عدم تكرار الكود
        if ProductDefinition.objects.filter(code=code).exclude(id=product.id).exists():
            messages.error(request, f'كود الصنف "{code}" موجود بالفعل.')
            return render(request, 'definitions/product_form.html', {
                'product': product,
                'action': 'edit',
                'form_data': request.POST,
                'categories': ProductCategory.objects.filter(is_active=True),
                'units': ['قطعة', 'كيلو', 'متر', 'لتر', 'علبة', 'كرتونة']
            })

        # التحقق من عدم تكرار الباركود
        if barcode and ProductDefinition.objects.filter(barcode=barcode).exclude(id=product.id).exists():
            messages.error(request, f'الباركود "{barcode}" موجود بالفعل.')
            return render(request, 'definitions/product_form.html', {
                'product': product,
                'action': 'edit',
                'form_data': request.POST,
                'categories': ProductCategory.objects.filter(is_active=True),
                'units': ['قطعة', 'كيلو', 'متر', 'لتر', 'علبة', 'كرتونة']
            })

        # الحصول على الفئة
        category = None
        if category_id:
            try:
                category = ProductCategory.objects.get(id=category_id)
            except ProductCategory.DoesNotExist:
                pass

        product.code = code
        product.name = name
        product.name_en = name_en
        product.barcode = barcode
        product.category = category
        product.main_unit = main_unit
        product.product_type = product_type
        product.description = description
        product.cost_price = cost_price
        product.selling_price = selling_price
        product.minimum_stock = minimum_stock
        product.maximum_stock = maximum_stock
        product.is_active = is_active
        product.track_inventory = track_inventory
        product.save()

        messages.success(request, f'تم تحديث الصنف "{product.name}" بنجاح!')
        return redirect('definitions:product_list')

    context = {
        'product': product,
        'action': 'edit',
        'categories': ProductCategory.objects.filter(is_active=True),
        'units': ['قطعة', 'كيلو', 'متر', 'لتر', 'علبة', 'كرتونة', 'جرام', 'طن', 'سم', 'مم']
    }

    return render(request, 'definitions/product_form.html', context)





@login_required
def product_quick_delete(request, product_id):
    """حذف سريع للصنف"""
    if request.method == 'POST':
        product = get_object_or_404(ProductDefinition, id=product_id)
        product_name = product.name
        product.delete()
        messages.success(request, f'تم حذف الصنف "{product_name}" بنجاح!')
        return redirect('definitions:product_list')

    return redirect('definitions:product_list')


# ===== أكواد الأصناف =====
@login_required
def product_code_list(request):
    """قائمة أكواد الأصناف"""
    from .models import ProductCode

    search_query = request.GET.get('search', '')
    code_type = request.GET.get('code_type', '')
    product_id = request.GET.get('product', '')

    codes = ProductCode.objects.select_related('product', 'created_by').all()

    if search_query:
        codes = codes.filter(
            Q(code__icontains=search_query) |
            Q(product__name__icontains=search_query) |
            Q(product__code__icontains=search_query) |
            Q(supplier_name__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    if code_type:
        codes = codes.filter(code_type=code_type)

    if product_id:
        codes = codes.filter(product_id=product_id)

    codes = codes.order_by('product__code', 'code_type', 'code')

    paginator = Paginator(codes, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # الحصول على الأصناف والأنواع للفلترة
    products = ProductDefinition.objects.filter(is_active=True).order_by('code')
    code_types = ProductCode.CODE_TYPES

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'code_type': code_type,
        'product_id': product_id,
        'products': products,
        'code_types': code_types,
    }

    return render(request, 'definitions/product_code_list.html', context)

@login_required
def product_code_create(request):
    """إنشاء كود صنف جديد"""
    from .models import ProductCode

    if request.method == 'POST':
        product_id = request.POST.get('product')
        code_type = request.POST.get('code_type')
        code = request.POST.get('code')
        description = request.POST.get('description', '')
        supplier_name = request.POST.get('supplier_name', '')
        reference_number = request.POST.get('reference_number', '')
        is_active = request.POST.get('is_active') == 'on'
        is_primary = request.POST.get('is_primary') == 'on'

        # التحقق من الحقول المطلوبة
        if not all([product_id, code_type, code]):
            messages.error(request, 'يرجى ملء جميع الحقول المطلوبة.')
            return render(request, 'definitions/product_code_form.html', {
                'form_data': request.POST,
                'products': ProductDefinition.objects.filter(is_active=True).order_by('code'),
                'code_types': ProductCode.CODE_TYPES,
            })

        # التحقق من عدم تكرار الكود
        if ProductCode.objects.filter(code_type=code_type, code=code).exists():
            messages.error(request, f'الكود "{code}" من نوع "{dict(ProductCode.CODE_TYPES)[code_type]}" موجود بالفعل.')
            return render(request, 'definitions/product_code_form.html', {
                'form_data': request.POST,
                'products': ProductDefinition.objects.filter(is_active=True).order_by('code'),
                'code_types': ProductCode.CODE_TYPES,
            })

        try:
            product = ProductDefinition.objects.get(id=product_id)

            product_code = ProductCode.objects.create(
                product=product,
                code_type=code_type,
                code=code,
                description=description,
                supplier_name=supplier_name,
                reference_number=reference_number,
                is_active=is_active,
                is_primary=is_primary,
                created_by=request.user
            )

            messages.success(request, f'تم إنشاء كود الصنف "{product_code.code}" بنجاح!')
            return redirect('definitions:product_code_list')

        except ProductDefinition.DoesNotExist:
            messages.error(request, 'الصنف المحدد غير موجود.')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إنشاء كود الصنف: {e}')

    context = {
        'action': 'create',
        'products': ProductDefinition.objects.filter(is_active=True).order_by('code'),
        'code_types': ProductCode.CODE_TYPES,
    }

    return render(request, 'definitions/product_code_form.html', context)

@login_required
def product_code_edit(request, code_id):
    """تعديل كود صنف"""
    from .models import ProductCode

    product_code = get_object_or_404(ProductCode, id=code_id)

    if request.method == 'POST':
        product_id = request.POST.get('product')
        code_type = request.POST.get('code_type')
        code = request.POST.get('code')
        description = request.POST.get('description', '')
        supplier_name = request.POST.get('supplier_name', '')
        reference_number = request.POST.get('reference_number', '')
        is_active = request.POST.get('is_active') == 'on'
        is_primary = request.POST.get('is_primary') == 'on'

        # التحقق من الحقول المطلوبة
        if not all([product_id, code_type, code]):
            messages.error(request, 'يرجى ملء جميع الحقول المطلوبة.')
            return render(request, 'definitions/product_code_form.html', {
                'product_code': product_code,
                'action': 'edit',
                'form_data': request.POST,
                'products': ProductDefinition.objects.filter(is_active=True).order_by('code'),
                'code_types': ProductCode.CODE_TYPES,
            })

        # التحقق من عدم تكرار الكود
        if ProductCode.objects.filter(code_type=code_type, code=code).exclude(id=product_code.id).exists():
            messages.error(request, f'الكود "{code}" من نوع "{dict(ProductCode.CODE_TYPES)[code_type]}" موجود بالفعل.')
            return render(request, 'definitions/product_code_form.html', {
                'product_code': product_code,
                'action': 'edit',
                'form_data': request.POST,
                'products': ProductDefinition.objects.filter(is_active=True).order_by('code'),
                'code_types': ProductCode.CODE_TYPES,
            })

        try:
            product = ProductDefinition.objects.get(id=product_id)

            product_code.product = product
            product_code.code_type = code_type
            product_code.code = code
            product_code.description = description
            product_code.supplier_name = supplier_name
            product_code.reference_number = reference_number
            product_code.is_active = is_active
            product_code.is_primary = is_primary
            product_code.save()

            messages.success(request, f'تم تحديث كود الصنف "{product_code.code}" بنجاح!')
            return redirect('definitions:product_code_list')

        except ProductDefinition.DoesNotExist:
            messages.error(request, 'الصنف المحدد غير موجود.')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء تحديث كود الصنف: {e}')

    context = {
        'product_code': product_code,
        'action': 'edit',
        'products': ProductDefinition.objects.filter(is_active=True).order_by('code'),
        'code_types': ProductCode.CODE_TYPES,
    }

    return render(request, 'definitions/product_code_form.html', context)

@login_required
def product_code_delete(request, code_id):
    """حذف كود صنف"""
    from .models import ProductCode

    product_code = get_object_or_404(ProductCode, id=code_id)

    if request.method == 'POST':
        code_name = product_code.code
        product_name = product_code.product.name
        product_code.delete()
        messages.success(request, f'تم حذف كود الصنف "{code_name}" للصنف "{product_name}" بنجاح!')
        return redirect('definitions:product_code_list')

    context = {
        'product_code': product_code,
    }

    return render(request, 'definitions/product_code_confirm_delete.html', context)

@login_required
def product_code_detail(request, code_id):
    """تفاصيل كود صنف"""
    from .models import ProductCode

    product_code = get_object_or_404(ProductCode, id=code_id)

    context = {
        'product_code': product_code,
    }

    return render(request, 'definitions/product_code_detail.html', context)


# ===== تعريف الأشخاص =====
@login_required
def person_list(request):
    """قائمة تعريف الأشخاص"""
    search_query = request.GET.get('search', '')
    person_type = request.GET.get('person_type', '')

    persons = PersonDefinition.objects.all()

    if search_query:
        persons = persons.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(phone__icontains=search_query) |
            Q(email__icontains=search_query) |
            Q(national_id__icontains=search_query)
        )

    if person_type:
        persons = persons.filter(person_type=person_type)

    persons = persons.order_by('code')

    paginator = Paginator(persons, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # الحصول على أنواع الأشخاص للفلترة
    person_types = PersonDefinition.PERSON_TYPES

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'person_type': person_type,
        'person_types': person_types,
        'total_persons': PersonDefinition.objects.count(),
        'active_persons': PersonDefinition.objects.filter(is_active=True).count(),
        'customers_count': PersonDefinition.objects.filter(person_type='customer').count(),
        'suppliers_count': PersonDefinition.objects.filter(person_type='supplier').count(),
        'employees_count': PersonDefinition.objects.filter(person_type='employee').count(),
    }

    return render(request, 'definitions/person_list.html', context)

def person_create(request):
    """إنشاء شخص جديد"""
    print(f"person_create called with method: {request.method}")
    print(f"User authenticated: {request.user.is_authenticated}")

    if request.method == 'POST':
        print("Processing POST request")
        print(f"POST data: {request.POST}")

        # إذا لم يكن المستخدم مسجل دخول، إعادة توجيه لتسجيل الدخول
        if not request.user.is_authenticated:
            print("User not authenticated, redirecting to login")
            from django.contrib.auth.views import redirect_to_login
            return redirect_to_login(request.get_full_path())

        # الحصول على البيانات من النموذج
        code = request.POST.get('code', '').strip()
        name = request.POST.get('name', '').strip()
        name_en = request.POST.get('name_en', '').strip()
        person_type = request.POST.get('person_type', '').strip()
        gender = request.POST.get('gender', '').strip()
        birth_date = request.POST.get('birth_date', '').strip()
        national_id = request.POST.get('national_id', '').strip()
        passport_number = request.POST.get('passport_number', '').strip()
        phone = request.POST.get('phone', '').strip()
        mobile = request.POST.get('mobile', '').strip()
        email = request.POST.get('email', '').strip()
        address = request.POST.get('address', '').strip()
        city = request.POST.get('city', '').strip()
        state = request.POST.get('state', '').strip()
        country = request.POST.get('country', '').strip()
        postal_code = request.POST.get('postal_code', '').strip()
        credit_limit = request.POST.get('credit_limit', '0')
        payment_terms = request.POST.get('payment_terms', '0')
        currency_id = request.POST.get('currency', '')
        tax_number = request.POST.get('tax_number', '').strip()
        tax_rate = request.POST.get('tax_rate', '0')
        notes = request.POST.get('notes', '').strip()
        is_active = request.POST.get('is_active') == 'on'

        print(f"Extracted data: code={code}, name={name}, person_type={person_type}")

        # التحقق من الحقول المطلوبة
        if not all([code, name, person_type]):
            messages.error(request, 'يرجى ملء جميع الحقول المطلوبة.')
            try:
                currencies = CurrencyDefinition.objects.filter(is_active=True)
            except:
                currencies = []
            return render(request, 'definitions/person_form.html', {
                'form_data': request.POST,
                'person_types': PersonDefinition.PERSON_TYPES,
                'gender_choices': PersonDefinition.GENDER_CHOICES,
                'currencies': currencies,
            })

        # التحقق من عدم تكرار الكود
        if PersonDefinition.objects.filter(code=code).exists():
            messages.error(request, f'كود الشخص "{code}" موجود بالفعل.')
            try:
                currencies = CurrencyDefinition.objects.filter(is_active=True)
            except:
                currencies = []
            return render(request, 'definitions/person_form.html', {
                'form_data': request.POST,
                'person_types': PersonDefinition.PERSON_TYPES,
                'gender_choices': PersonDefinition.GENDER_CHOICES,
                'currencies': currencies,
            })

        try:
            print("Starting person creation...")

            # تحويل التواريخ والأرقام بأمان
            birth_date_obj = None
            if birth_date:
                try:
                    from datetime import datetime
                    birth_date_obj = datetime.strptime(birth_date, '%Y-%m-%d').date()
                    print(f"Birth date converted: {birth_date_obj}")
                except ValueError as e:
                    print(f"Birth date conversion error: {e}")
                    birth_date_obj = None

            try:
                credit_limit = float(credit_limit) if credit_limit else 0.0
            except (ValueError, TypeError):
                credit_limit = 0.0

            try:
                payment_terms = int(payment_terms) if payment_terms else 0
            except (ValueError, TypeError):
                payment_terms = 0

            try:
                tax_rate = float(tax_rate) if tax_rate else 0.0
            except (ValueError, TypeError):
                tax_rate = 0.0

            # معالجة العملة
            currency_obj = None
            if currency_id:
                try:
                    currency_obj = CurrencyDefinition.objects.get(id=currency_id)
                    print(f"Currency found: {currency_obj.name}")
                except CurrencyDefinition.DoesNotExist:
                    print(f"Currency with id {currency_id} not found")

            print(f"Creating person with: code={code}, name={name}, type={person_type}")

            person = PersonDefinition.objects.create(
                code=code,
                name=name,
                name_en=name_en or '',
                person_type=person_type,
                gender=gender or '',
                birth_date=birth_date_obj,
                national_id=national_id or '',
                passport_number=passport_number or '',
                phone=phone or '',
                mobile=mobile or '',
                email=email or '',
                address=address or '',
                city=city or '',
                state=state or '',
                country=country or '',
                postal_code=postal_code or '',
                credit_limit=credit_limit,
                payment_terms=payment_terms,
                currency=currency_obj,
                tax_number=tax_number or '',
                tax_rate=tax_rate,
                notes=notes or '',
                is_active=is_active,
                created_by=request.user
            )

            print(f"Person created successfully: {person.id}")
            messages.success(request, f'تم إنشاء تعريف الشخص "{person.name}" بنجاح!')
            print("Redirecting to person_list")
            return redirect('definitions:person_list')

        except Exception as e:
            print(f"Error creating person: {e}")
            import traceback
            traceback.print_exc()
            messages.error(request, f'حدث خطأ أثناء إنشاء تعريف الشخص: {e}')
            try:
                currencies = CurrencyDefinition.objects.filter(is_active=True)
            except:
                currencies = []
            return render(request, 'definitions/person_form.html', {
                'action': 'create',
                'form_data': request.POST,
                'person_types': PersonDefinition.PERSON_TYPES,
                'gender_choices': PersonDefinition.GENDER_CHOICES,
                'currencies': currencies,
            })

    # معالجة GET request
    print("Processing GET request - showing form")

    # إذا لم يكن المستخدم مسجل دخول، إعادة توجيه لتسجيل الدخول
    if not request.user.is_authenticated:
        print("User not authenticated for GET, redirecting to login")
        from django.contrib.auth.views import redirect_to_login
        return redirect_to_login(request.get_full_path())

    # الحصول على العملات بأمان
    try:
        currencies = CurrencyDefinition.objects.filter(is_active=True)
        print(f"Found {currencies.count()} currencies")
    except Exception as e:
        print(f"Error getting currencies: {e}")
        currencies = []

    context = {
        'action': 'create',
        'person_types': PersonDefinition.PERSON_TYPES,
        'gender_choices': PersonDefinition.GENDER_CHOICES,
        'currencies': currencies,
    }

    print("Rendering person_form.html")
    return render(request, 'definitions/person_form.html', context)

@login_required
def person_edit(request, person_id):
    """تعديل شخص"""
    person = get_object_or_404(PersonDefinition, id=person_id)

    if request.method == 'POST':
        code = request.POST.get('code')
        name = request.POST.get('name')
        name_en = request.POST.get('name_en', '')
        person_type = request.POST.get('person_type')
        gender = request.POST.get('gender', '')
        birth_date = request.POST.get('birth_date', '')
        national_id = request.POST.get('national_id', '')
        passport_number = request.POST.get('passport_number', '')
        phone = request.POST.get('phone', '')
        mobile = request.POST.get('mobile', '')
        email = request.POST.get('email', '')
        address = request.POST.get('address', '')
        city = request.POST.get('city', '')
        state = request.POST.get('state', '')
        country = request.POST.get('country', '')
        postal_code = request.POST.get('postal_code', '')
        credit_limit = request.POST.get('credit_limit', 0)
        payment_terms = request.POST.get('payment_terms', 0)
        tax_number = request.POST.get('tax_number', '')
        tax_rate = request.POST.get('tax_rate', 0)
        notes = request.POST.get('notes', '')
        is_active = request.POST.get('is_active') == 'on'

        # التحقق من الحقول المطلوبة
        if not all([code, name, person_type]):
            messages.error(request, 'يرجى ملء جميع الحقول المطلوبة.')
            return render(request, 'definitions/person_form.html', {
                'person': person,
                'action': 'edit',
                'form_data': request.POST,
                'person_types': PersonDefinition.PERSON_TYPES,
                'gender_choices': PersonDefinition.GENDER_CHOICES,
                'currencies': CurrencyDefinition.objects.filter(is_active=True),
            })

        # التحقق من عدم تكرار الكود
        if PersonDefinition.objects.filter(code=code).exclude(id=person.id).exists():
            messages.error(request, f'كود الشخص "{code}" موجود بالفعل.')
            return render(request, 'definitions/person_form.html', {
                'person': person,
                'action': 'edit',
                'form_data': request.POST,
                'person_types': PersonDefinition.PERSON_TYPES,
                'gender_choices': PersonDefinition.GENDER_CHOICES,
                'currencies': CurrencyDefinition.objects.filter(is_active=True),
            })

        try:
            # تحويل التواريخ والأرقام
            birth_date_obj = None
            if birth_date:
                from datetime import datetime
                birth_date_obj = datetime.strptime(birth_date, '%Y-%m-%d').date()

            credit_limit = float(credit_limit) if credit_limit else 0
            payment_terms = int(payment_terms) if payment_terms else 0
            tax_rate = float(tax_rate) if tax_rate else 0

            person.code = code
            person.name = name
            person.name_en = name_en
            person.person_type = person_type
            person.gender = gender
            person.birth_date = birth_date_obj
            person.national_id = national_id
            person.passport_number = passport_number
            person.phone = phone
            person.mobile = mobile
            person.email = email
            person.address = address
            person.city = city
            person.state = state
            person.country = country
            person.postal_code = postal_code
            person.credit_limit = credit_limit
            person.payment_terms = payment_terms
            person.tax_number = tax_number
            person.tax_rate = tax_rate
            person.notes = notes
            person.is_active = is_active
            person.save()

            messages.success(request, f'تم تحديث تعريف الشخص "{person.name}" بنجاح!')
            return redirect('definitions:person_list')

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء تحديث تعريف الشخص: {e}')

    context = {
        'person': person,
        'action': 'edit',
        'person_types': PersonDefinition.PERSON_TYPES,
        'gender_choices': PersonDefinition.GENDER_CHOICES,
        'currencies': CurrencyDefinition.objects.filter(is_active=True),
    }

    return render(request, 'definitions/person_form.html', context)

@login_required
def person_delete(request, person_id):
    """حذف شخص"""
    person = get_object_or_404(PersonDefinition, id=person_id)

    if request.method == 'POST':
        person_name = person.name
        person.delete()
        messages.success(request, f'تم حذف تعريف الشخص "{person_name}" بنجاح!')
        return redirect('definitions:person_list')

    context = {
        'person': person,
    }

    return render(request, 'definitions/person_confirm_delete.html', context)

@login_required
def person_detail(request, person_id):
    """تفاصيل شخص"""
    person = get_object_or_404(PersonDefinition, id=person_id)

    context = {
        'person': person,
    }

    return render(request, 'definitions/person_detail.html', context)

# ===== فئات الأصناف =====
@login_required
def category_list(request):
    """قائمة فئات الأصناف"""
    search_query = request.GET.get('search', '')

    categories = ProductCategory.objects.all()

    if search_query:
        categories = categories.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    categories = categories.order_by('code')

    paginator = Paginator(categories, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'total_categories': ProductCategory.objects.count(),
    }

    return render(request, 'definitions/category_list.html', context)

@login_required
def category_create(request):
    """إنشاء فئة أصناف جديدة"""
    if request.method == 'POST':
        code = request.POST.get('code')
        name = request.POST.get('name')
        description = request.POST.get('description', '')
        parent_id = request.POST.get('parent_category')
        is_active = request.POST.get('is_active') == 'on'

        # التحقق من عدم تكرار الكود
        if ProductCategory.objects.filter(code=code).exists():
            messages.error(request, f'كود الفئة "{code}" موجود بالفعل.')
            return render(request, 'definitions/category_form.html', {
                'form_data': request.POST,
                'parent_categories': ProductCategory.objects.filter(parent__isnull=True, is_active=True)
            })

        # الحصول على الفئة الأب إذا تم تحديدها
        parent_category = None
        if parent_id:
            try:
                parent_category = ProductCategory.objects.get(id=parent_id)
            except ProductCategory.DoesNotExist:
                pass

        category = ProductCategory.objects.create(
            code=code,
            name=name,
            description=description,
            parent=parent_category,
            is_active=is_active,
            created_by=request.user
        )

        messages.success(request, f'تم إنشاء فئة الأصناف "{category.name}" بنجاح!')
        return redirect('definitions:category_list')

    context = {
        'action': 'create',
        'parent_categories': ProductCategory.objects.filter(parent__isnull=True, is_active=True)
    }

    return render(request, 'definitions/category_form.html', context)

@login_required
def category_edit(request, category_id):
    """تعديل فئة أصناف موجودة"""
    category = get_object_or_404(ProductCategory, id=category_id)

    if request.method == 'POST':
        code = request.POST.get('code')
        name = request.POST.get('name')
        description = request.POST.get('description', '')
        parent_id = request.POST.get('parent_category')
        is_active = request.POST.get('is_active') == 'on'

        # التحقق من عدم تكرار الكود
        if ProductCategory.objects.filter(code=code).exclude(id=category.id).exists():
            messages.error(request, f'كود الفئة "{code}" موجود بالفعل.')
            return render(request, 'definitions/category_form.html', {
                'category': category,
                'action': 'edit',
                'form_data': request.POST,
                'parent_categories': ProductCategory.objects.filter(parent__isnull=True, is_active=True).exclude(id=category.id)
            })

        # الحصول على الفئة الأب إذا تم تحديدها
        parent_category = None
        if parent_id:
            try:
                parent_category = ProductCategory.objects.get(id=parent_id)
                # منع جعل الفئة أب لنفسها
                if parent_category == category:
                    messages.error(request, 'لا يمكن جعل الفئة أب لنفسها.')
                    return render(request, 'definitions/category_form.html', {
                        'category': category,
                        'action': 'edit',
                        'form_data': request.POST,
                        'parent_categories': ProductCategory.objects.filter(parent__isnull=True, is_active=True).exclude(id=category.id)
                    })
            except ProductCategory.DoesNotExist:
                pass

        category.code = code
        category.name = name
        category.description = description
        category.parent = parent_category
        category.is_active = is_active
        category.save()

        messages.success(request, f'تم تحديث فئة الأصناف "{category.name}" بنجاح!')
        return redirect('definitions:category_list')

    context = {
        'category': category,
        'action': 'edit',
        'parent_categories': ProductCategory.objects.filter(parent__isnull=True, is_active=True).exclude(id=category.id)
    }

    return render(request, 'definitions/category_form.html', context)

@login_required
def category_detail(request, category_id):
    """تفاصيل فئة الأصناف"""
    category = get_object_or_404(ProductCategory, id=category_id)

    # الحصول على الفئات الفرعية
    subcategories = ProductCategory.objects.filter(parent=category)

    # الحصول على الأصناف في هذه الفئة
    products = ProductDefinition.objects.filter(category=category)

    context = {
        'category': category,
        'subcategories': subcategories,
        'products': products,
    }

    return render(request, 'definitions/category_detail.html', context)

@login_required
def category_delete(request, category_id):
    """حذف فئة أصناف"""
    category = get_object_or_404(ProductCategory, id=category_id)

    if request.method == 'POST':
        # التحقق من وجود فئات فرعية
        subcategories_count = ProductCategory.objects.filter(parent=category).count()
        products_count = ProductDefinition.objects.filter(category=category).count()

        if subcategories_count > 0 or products_count > 0:
            messages.error(
                request,
                f'لا يمكن حذف الفئة "{category.name}" لأنها تحتوي على '
                f'({subcategories_count} فئة فرعية، {products_count} صنف). '
                'يرجى حذف البيانات المرتبطة أولاً أو إلغاء تفعيل الفئة.'
            )
            return redirect('definitions:category_detail', category_id=category.id)

        category_name = category.name
        category.delete()
        messages.success(request, f'تم حذف فئة الأصناف "{category_name}" بنجاح!')
        return redirect('definitions:category_list')

    context = {
        'category': category,
        'subcategories_count': ProductCategory.objects.filter(parent=category).count(),
        'products_count': ProductDefinition.objects.filter(category=category).count(),
    }

    return render(request, 'definitions/category_confirm_delete.html', context)

@login_required
def category_quick_delete(request, category_id):
    """حذف سريع لفئة الأصناف"""
    if request.method == 'POST':
        category = get_object_or_404(ProductCategory, id=category_id)

        # التحقق من وجود بيانات مرتبطة
        subcategories_count = ProductCategory.objects.filter(parent=category).count()
        products_count = ProductDefinition.objects.filter(category=category).count()

        if subcategories_count > 0 or products_count > 0:
            messages.error(
                request,
                f'لا يمكن حذف الفئة "{category.name}" لأنها تحتوي على بيانات مرتبطة. '
                'يرجى استخدام صفحة التفاصيل للحذف الآمن.'
            )
        else:
            category_name = category.name
            category.delete()
            messages.success(request, f'تم حذف فئة الأصناف "{category_name}" بنجاح!')

        return redirect('definitions:category_list')

    return redirect('definitions:category_list')
