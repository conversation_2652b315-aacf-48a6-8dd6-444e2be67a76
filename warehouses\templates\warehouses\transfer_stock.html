{% extends 'base.html' %}
{% load static %}

{% block title %}نقل مخزون بين المخازن{% endblock %}

{% block extra_css %}
<style>
    .transfer-header {
        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        padding: 2rem 0;
        margin-bottom: 2rem;
        color: white;
        border-radius: 15px;
    }

    .form-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .transfer-arrow {
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 1rem 0;
    }

    .transfer-arrow i {
        font-size: 2rem;
        color: #ffc107;
    }

    .btn-transfer {
        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        font-weight: 600;
        color: white;
        transition: all 0.3s ease;
    }

    .btn-transfer:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
        color: white;
    }

    .warehouse-card {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 1rem;
        text-align: center;
        transition: all 0.3s ease;
    }

    .warehouse-card.selected {
        border-color: #ffc107;
        background: #fff3cd;
    }

    .warehouse-card:hover {
        border-color: #ffc107;
    }
</style>
{% endblock %}

{% block content %}
<!-- Transfer Header -->
<div class="transfer-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-arrow-left-right me-2"></i>
                    نقل مخزون بين المخازن
                </h1>
                <p class="mb-0 opacity-75">نقل الأصناف من مخزن إلى آخر</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'warehouses:dashboard' %}" class="btn btn-light">
                    <i class="bi bi-arrow-right me-1"></i>العودة للمخازن
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Transfer Form -->
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="form-card">
                <form method="post" id="transferForm">
                    {% csrf_token %}
                    
                    <!-- Warehouse Selection -->
                    <div class="row">
                        <div class="col-md-5">
                            <h5 class="mb-3">
                                <i class="bi bi-building me-2"></i>من المخزن
                            </h5>
                            <div class="form-group">
                                <select name="from_warehouse" id="fromWarehouse" class="form-select" required>
                                    <option value="">اختر المخزن المرسل</option>
                                    {% for warehouse in warehouses %}
                                        <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-2">
                            <div class="transfer-arrow">
                                <i class="bi bi-arrow-right"></i>
                            </div>
                        </div>
                        
                        <div class="col-md-5">
                            <h5 class="mb-3">
                                <i class="bi bi-building me-2"></i>إلى المخزن
                            </h5>
                            <div class="form-group">
                                <select name="to_warehouse" id="toWarehouse" class="form-select" required>
                                    <option value="">اختر المخزن المستقبل</option>
                                    {% for warehouse in warehouses %}
                                        <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <!-- Product and Quantity -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="product" class="form-label">الصنف المراد نقله</label>
                                <select name="product" id="product" class="form-select" required>
                                    <option value="">اختر الصنف</option>
                                    {% for product in products %}
                                        <option value="{{ product.id }}">{{ product.name }} ({{ product.code }})</option>
                                    {% endfor %}
                                </select>
                                <div id="stockInfo" class="mt-2" style="display: none;">
                                    <small class="text-muted">
                                        الكمية المتوفرة في المخزن المرسل: 
                                        <span id="availableStock" class="fw-bold text-success">0</span>
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="quantity" class="form-label">الكمية المراد نقلها</label>
                                <input type="number" name="quantity" id="quantity" class="form-control" 
                                       step="0.001" min="0" required placeholder="أدخل الكمية">
                                <div id="quantityWarning" class="mt-1" style="display: none;">
                                    <small class="text-warning">
                                        <i class="bi bi-exclamation-triangle me-1"></i>
                                        الكمية المطلوبة أكبر من المتوفر
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="notes" class="form-label">ملاحظات النقل (اختياري)</label>
                        <textarea name="notes" id="notes" class="form-control" rows="3" 
                                  placeholder="سبب النقل أو أي ملاحظات أخرى..."></textarea>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-transfer btn-lg" id="submitBtn">
                            <i class="bi bi-arrow-left-right me-2"></i>
                            تنفيذ عملية النقل
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Transfer Info -->
    <div class="row">
        <div class="col-12">
            <div class="alert alert-info">
                <h6><i class="bi bi-info-circle me-2"></i>معلومات عملية النقل:</h6>
                <ul class="mb-0">
                    <li>سيتم خصم الكمية من المخزن المرسل وإضافتها للمخزن المستقبل</li>
                    <li>سيتم إنشاء حركتين: حركة إخراج وحركة إدخال</li>
                    <li>سيتم الاحتفاظ بنفس تكلفة الوحدة</li>
                    <li>لا يمكن نقل كمية أكبر من المتوفر في المخزن المرسل</li>
                    <li>لا يمكن النقل من وإلى نفس المخزن</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const fromWarehouse = document.getElementById('fromWarehouse');
    const toWarehouse = document.getElementById('toWarehouse');
    const productSelect = document.getElementById('product');
    const quantityInput = document.getElementById('quantity');
    const stockInfo = document.getElementById('stockInfo');
    const availableStock = document.getElementById('availableStock');
    const quantityWarning = document.getElementById('quantityWarning');
    const submitBtn = document.getElementById('submitBtn');
    
    let currentStock = 0;
    
    // منع اختيار نفس المخزن
    function validateWarehouses() {
        if (fromWarehouse.value && toWarehouse.value && fromWarehouse.value === toWarehouse.value) {
            alert('لا يمكن النقل من وإلى نفس المخزن');
            toWarehouse.value = '';
        }
    }
    
    // التحقق من الرصيد المتوفر
    function checkStock() {
        const fromWarehouseId = fromWarehouse.value;
        const productId = productSelect.value;
        
        if (fromWarehouseId && productId) {
            // هنا يمكن إضافة AJAX call للحصول على الرصيد الفعلي
            // مؤقتاً سنستخدم قيم وهمية
            currentStock = Math.floor(Math.random() * 100) + 10;
            availableStock.textContent = currentStock;
            stockInfo.style.display = 'block';
        } else {
            stockInfo.style.display = 'none';
        }
    }
    
    // التحقق من الكمية المطلوبة
    function validateQuantity() {
        const requestedQuantity = parseFloat(quantityInput.value) || 0;
        
        if (requestedQuantity > currentStock) {
            quantityWarning.style.display = 'block';
            submitBtn.disabled = true;
            submitBtn.classList.add('disabled');
        } else {
            quantityWarning.style.display = 'none';
            submitBtn.disabled = false;
            submitBtn.classList.remove('disabled');
        }
    }
    
    fromWarehouse.addEventListener('change', function() {
        validateWarehouses();
        checkStock();
    });
    
    toWarehouse.addEventListener('change', validateWarehouses);
    productSelect.addEventListener('change', checkStock);
    quantityInput.addEventListener('input', validateQuantity);
    
    // منع الإرسال إذا كانت البيانات غير صحيحة
    document.getElementById('transferForm').addEventListener('submit', function(e) {
        const fromWarehouseId = fromWarehouse.value;
        const toWarehouseId = toWarehouse.value;
        const requestedQuantity = parseFloat(quantityInput.value) || 0;
        
        if (fromWarehouseId === toWarehouseId) {
            e.preventDefault();
            alert('لا يمكن النقل من وإلى نفس المخزن');
            return;
        }
        
        if (requestedQuantity > currentStock) {
            e.preventDefault();
            alert('لا يمكن نقل كمية أكبر من المتوفر في المخزون');
            return;
        }
        
        if (!confirm('هل أنت متأكد من تنفيذ عملية النقل؟')) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
