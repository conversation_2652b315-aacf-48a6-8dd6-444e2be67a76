{% extends 'base.html' %}
{% load static %}

{% block title %}تعريف الطابعات - أوساريك{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
        min-height: 100vh;
    }

    .page-header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 15px;
        box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        border: 1px solid rgba(255, 255, 255, 0.18);
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 800;
        background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin: 0;
        text-align: center;
    }

    .page-subtitle {
        color: #6c757d;
        margin: 0.5rem 0 0 0;
        text-align: center;
        font-size: 1.1rem;
    }

    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 2rem;
        text-align: center;
        box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        border: 1px solid rgba(255, 255, 255, 0.18);
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 40px rgba(31, 38, 135, 0.5);
    }

    .stat-number {
        font-size: 3rem;
        font-weight: 800;
        background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #6c757d;
        font-size: 1rem;
        font-weight: 600;
    }

    .table-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        border: 1px solid rgba(255, 255, 255, 0.18);
    }

    .table {
        margin: 0;
        font-size: 0.9rem;
    }

    .table th {
        background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
        color: white;
        border: none;
        font-weight: 700;
        padding: 1.5rem 1rem;
        text-align: center;
    }

    .table td {
        padding: 1rem;
        vertical-align: middle;
        border-bottom: 1px solid rgba(0,0,0,0.1);
        text-align: center;
    }

    .table tbody tr {
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background: linear-gradient(135deg, rgba(52, 73, 94, 0.1) 0%, rgba(44, 62, 80, 0.1) 100%);
        transform: scale(1.02);
    }

    .btn {
        padding: 0.75rem 1.5rem;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        font-size: 0.9rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .btn-primary {
        background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(52, 73, 94, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-outline-secondary {
        background: transparent;
        border: 2px solid #6c757d;
        color: #6c757d;
    }

    .btn-outline-secondary:hover {
        background: #6c757d;
        color: white;
        text-decoration: none;
        transform: translateY(-3px);
    }

    .btn-outline-info {
        background: transparent;
        border: 2px solid #17a2b8;
        color: #17a2b8;
    }

    .btn-outline-info:hover {
        background: #17a2b8;
        color: white;
        text-decoration: none;
        transform: translateY(-3px);
    }

    .btn-outline-success {
        background: transparent;
        border: 2px solid #28a745;
        color: #28a745;
    }

    .btn-outline-success:hover {
        background: #28a745;
        color: white;
        text-decoration: none;
        transform: translateY(-3px);
    }

    .btn-outline-danger {
        background: transparent;
        border: 2px solid #dc3545;
        color: #dc3545;
    }

    .btn-outline-danger:hover {
        background: #dc3545;
        color: white;
        text-decoration: none;
        transform: translateY(-3px);
    }

    .badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .badge.active {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
    }

    .badge.inactive {
        background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        color: white;
    }

    .badge.thermal {
        background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
        color: white;
    }

    .badge.inkjet {
        background: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%);
        color: white;
    }

    .badge.laser {
        background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
        color: white;
    }

    .badge.dot_matrix {
        background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        color: white;
    }

    .empty-state {
        text-align: center;
        padding: 4rem;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1.5rem;
        background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .action-buttons {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .printer-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        margin: 0 auto;
    }

    .connection-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.25rem 0.75rem;
        background: rgba(52, 73, 94, 0.1);
        border-radius: 15px;
        font-size: 0.8rem;
        color: #34495e;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- CSRF Token for JavaScript -->
    {% csrf_token %}
    
    <!-- Page Header -->
    <div class="page-header">
        <h1 class="page-title">
            <i class="bi bi-printer me-3"></i>تعريف الطابعات
        </h1>
        <p class="page-subtitle">إدارة وتكوين الطابعات المتصلة بالنظام</p>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
        <a href="{% url 'definitions:dashboard' %}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right me-2"></i>عودة للتعريفات
        </a>
        <a href="{% url 'definitions:printer_create' %}" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>إضافة طابعة جديدة
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-cards">
        <div class="stat-card">
            <div class="stat-number">{{ total_printers }}</div>
            <div class="stat-label">إجمالي الطابعات</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ active_printers }}</div>
            <div class="stat-label">الطابعات النشطة</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ default_printers }}</div>
            <div class="stat-label">الطابعات الافتراضية</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ network_printers }}</div>
            <div class="stat-label">طابعات الشبكة</div>
        </div>
    </div>

    <!-- Messages -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert" style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 15px; border: 1px solid rgba(255, 255, 255, 0.18);">
                <i class="bi bi-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% else %}info-circle{% endif %} me-2"></i>
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- Printers Table -->
    <div class="table-container">
        {% if printers %}
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الأيقونة</th>
                        <th>الاسم</th>
                        <th>النوع</th>
                        <th>الاتصال</th>
                        <th>الحالة</th>
                        <th>افتراضية</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for printer in printers %}
                        <tr>
                            <td>
                                <div class="printer-icon">
                                    <i class="bi bi-printer"></i>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ printer.name }}</strong>
                                    {% if printer.description %}
                                        <br><small class="text-muted">{{ printer.description }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <span class="badge {{ printer.printer_type }}">
                                    {% if printer.printer_type == 'thermal' %}
                                        <i class="bi bi-thermometer me-1"></i>حرارية
                                    {% elif printer.printer_type == 'inkjet' %}
                                        <i class="bi bi-droplet me-1"></i>نفث حبر
                                    {% elif printer.printer_type == 'laser' %}
                                        <i class="bi bi-lightning me-1"></i>ليزر
                                    {% else %}
                                        <i class="bi bi-grid-3x3 me-1"></i>نقطية
                                    {% endif %}
                                </span>
                            </td>
                            <td>
                                <span class="connection-badge">
                                    {% if printer.connection_type == 'usb' %}
                                        <i class="bi bi-usb"></i>USB
                                    {% elif printer.connection_type == 'network' %}
                                        <i class="bi bi-wifi"></i>شبكة
                                    {% elif printer.connection_type == 'bluetooth' %}
                                        <i class="bi bi-bluetooth"></i>بلوتوث
                                    {% else %}
                                        <i class="bi bi-hdd"></i>محلي
                                    {% endif %}
                                </span>
                            </td>
                            <td>
                                <span class="badge {% if printer.is_active %}active{% else %}inactive{% endif %}">
                                    {% if printer.is_active %}
                                        <i class="bi bi-check-circle me-1"></i>نشط
                                    {% else %}
                                        <i class="bi bi-x-circle me-1"></i>غير نشط
                                    {% endif %}
                                </span>
                            </td>
                            <td>
                                {% if printer.is_default %}
                                    <i class="bi bi-star-fill text-warning" title="طابعة افتراضية"></i>
                                {% else %}
                                    <i class="bi bi-star text-muted" title="ليست افتراضية"></i>
                                {% endif %}
                            </td>
                            <td>
                                <div class="d-flex gap-1 justify-content-center">
                                    <a href="{% url 'definitions:printer_detail' printer.id %}" 
                                       class="btn btn-sm btn-outline-info" 
                                       title="عرض التفاصيل">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{% url 'definitions:printer_edit' printer.id %}" 
                                       class="btn btn-sm btn-outline-success" 
                                       title="تعديل">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <button type="button" 
                                            class="btn btn-sm btn-outline-danger" 
                                            title="حذف"
                                            onclick="deletePrinter({{ printer.id }}, '{{ printer.name|escapejs }}')">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        {% else %}
            <div class="empty-state">
                <i class="bi bi-printer"></i>
                <h3>لا توجد طابعات</h3>
                <p>لم يتم العثور على طابعات مطابقة للبحث.</p>
                <a href="{% url 'definitions:printer_create' %}" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>إضافة أول طابعة
                </a>
            </div>
        {% endif %}
    </div>
</div>

<script>
function deletePrinter(printerId, printerName) {
    if (confirm('هل أنت متأكد من حذف الطابعة "' + printerName + '"؟\n\nهذا الإجراء لا يمكن التراجع عنه!')) {
        // إنشاء نموذج مخفي وإرساله
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = '/definitions/printers/' + printerId + '/quick-delete/';
        
        // إضافة CSRF token
        var csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
        var csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrfmiddlewaretoken';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);
        
        // إضافة النموذج للصفحة وإرساله
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
