{% extends 'base.html' %}

{% block title %}إدارة الوحدات - أوساريك{% endblock %}

{% block content %}
    <div class="page-header d-flex justify-content-between align-items-center">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'dashboard_home' %}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'definitions:dashboard' %}">التعريفات</a></li>
                    <li class="breadcrumb-item active">الوحدات</li>
                </ol>
            </nav>
            <h1 class="page-title">إدارة الوحدات</h1>
            <p class="page-subtitle">عرض وإدارة جميع وحدات القياس والتحويل</p>
        </div>
        <a href="{% url 'definitions:unit_create' %}" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>إضافة وحدة جديدة
        </a>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-8">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-search"></i>
                        </span>
                        <input type="text" 
                               class="form-control" 
                               name="search" 
                               value="{{ search_query }}" 
                               placeholder="البحث في أسماء الوحدات أو الرموز...">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="bi bi-search me-1"></i>بحث
                        </button>
                        <a href="{% url 'definitions:unit_list' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-clockwise me-1"></i>إعادة تعيين
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Units Grid -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">قائمة الوحدات</h5>
            <span class="badge bg-primary">{{ page_obj.paginator.count }} وحدة</span>
        </div>
        <div class="card-body">
            {% if page_obj %}
                <div class="row g-4">
                    {% for unit in page_obj %}
                        <div class="col-lg-4 col-md-6">
                            <div class="card h-100 unit-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div class="unit-icon bg-success text-white rounded-circle d-flex align-items-center justify-content-center">
                                            <i class="bi bi-rulers"></i>
                                        </div>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                <i class="bi bi-three-dots"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="{% url 'definitions:unit_edit' unit.pk %}">
                                                    <i class="bi bi-pencil me-2"></i>تعديل
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="{% url 'definitions:unit_delete' unit.pk %}">
                                                    <i class="bi bi-trash me-2"></i>حذف
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>
                                    
                                    <h5 class="card-title">{{ unit.name }}</h5>
                                    <p class="text-muted small mb-2">الرمز: {{ unit.symbol }}</p>
                                    
                                    <div class="mb-3">
                                        <span class="badge bg-info">{{ unit.get_unit_type_display }}</span>
                                        {% if not unit.is_active %}
                                            <span class="badge bg-danger">غير نشط</span>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="row text-center mb-3">
                                        <div class="col-6">
                                            <div class="border-end">
                                                <h6 class="mb-0">{{ unit.conversion_factor }}</h6>
                                                <small class="text-muted">معامل التحويل</small>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <h6 class="mb-0">
                                                {% if unit.base_unit %}
                                                    {{ unit.base_unit.symbol }}
                                                {% else %}
                                                    أساسية
                                                {% endif %}
                                            </h6>
                                            <small class="text-muted">الوحدة الأساسية</small>
                                        </div>
                                    </div>
                                    
                                    <div class="d-flex gap-2">
                                        <a href="{% url 'definitions:unit_edit' unit.pk %}" class="btn btn-outline-primary btn-sm flex-fill">
                                            <i class="bi bi-pencil me-1"></i>تعديل
                                        </a>
                                        <a href="{% url 'definitions:unit_delete' unit.pk %}" class="btn btn-outline-danger btn-sm">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                    <nav aria-label="صفحات الوحدات" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}">الأولى</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">السابقة</a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">
                                    صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">التالية</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}">الأخيرة</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-rulers text-muted" style="font-size: 4rem;"></i>
                    <h4 class="mt-3 text-muted">لا توجد وحدات</h4>
                    <p class="text-muted">لم يتم العثور على أي وحدات مطابقة لمعايير البحث.</p>
                    <a href="{% url 'definitions:unit_create' %}" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>إضافة أول وحدة
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block extra_css %}
<style>
    .unit-card {
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    
    .unit-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }
    
    .unit-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }
    
    .border-end {
        border-right: 1px solid #dee2e6 !important;
    }
    
    .breadcrumb {
        background-color: transparent;
        padding: 0;
        margin-bottom: 1rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        content: "←";
    }
</style>
{% endblock %}
