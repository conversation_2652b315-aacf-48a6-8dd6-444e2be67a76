{% extends 'base.html' %}
{% load static %}

{% block title %}
{% if object %}تعديل الصنف - {{ object.name }}{% else %}إضافة صنف جديد{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    /* Advanced Product Form Styles */
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --danger-gradient: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
        --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
    }

    /* Hero Section */
    .form-hero {
        background: var(--primary-gradient);
        padding: 3rem 0;
        margin-bottom: 3rem;
        position: relative;
        overflow: hidden;
        border-radius: 0 0 2rem 2rem;
    }

    .form-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    }

    .hero-content {
        position: relative;
        z-index: 2;
        color: white;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
        font-weight: 800;
        margin-bottom: 0.5rem;
        text-shadow: 0 4px 20px rgba(0,0,0,0.3);
    }

    .hero-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
    }

    /* Form Container */
    .form-container {
        background: white;
        border-radius: 24px;
        padding: 3rem;
        box-shadow: 0 15px 50px rgba(0,0,0,0.1);
        border: 1px solid #e5e7eb;
        position: relative;
        overflow: hidden;
    }

    .form-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6px;
        background: var(--primary-gradient);
    }

    /* Form Sections */
    .form-section {
        margin-bottom: 3rem;
        padding: 2rem;
        background: #f8fafc;
        border-radius: 16px;
        border: 1px solid #e5e7eb;
        position: relative;
        overflow: hidden;
    }

    .form-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: var(--info-gradient);
    }

    .section-title {
        font-size: 1.4rem;
        font-weight: 700;
        color: #374151;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .section-icon {
        width: 40px;
        height: 40px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
    }

    /* Form Controls */
    .form-label {
        font-weight: 700;
        color: #374151;
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.95rem;
    }

    .form-control, .form-select {
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        padding: 1rem 1.25rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: white;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
        background: white;
    }

    .form-control.is-invalid, .form-select.is-invalid {
        border-color: #ef4444;
        box-shadow: 0 0 0 0.25rem rgba(239, 68, 68, 0.25);
    }

    .invalid-feedback {
        display: block;
        color: #ef4444;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        font-weight: 600;
    }

    /* Input Groups */
    .input-group {
        position: relative;
    }

    .input-group-text {
        background: var(--primary-gradient);
        color: white;
        border: none;
        border-radius: 12px 0 0 12px;
        font-weight: 600;
        padding: 1rem 1.25rem;
    }

    .input-group .form-control {
        border-radius: 0 12px 12px 0;
        border-left: none;
    }

    /* Required Field Indicator */
    .required::after {
        content: ' *';
        color: #ef4444;
        font-weight: bold;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .form-container {
            padding: 2rem;
            margin: 1rem;
        }

        .form-section {
            padding: 1.5rem;
        }

        .hero-title {
            font-size: 2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="form-hero">
    <div class="container">
        <div class="hero-content">
            <h1 class="hero-title">
                <i class="bi bi-box-seam me-3"></i>
                {% if object %}تعديل الصنف{% else %}إضافة صنف جديد{% endif %}
            </h1>
            <p class="hero-subtitle">
                {% if object %}تحديث معلومات الصنف في الكتالوج{% else %}إضافة صنف جديد إلى كتالوج المنتجات{% endif %}
            </p>
        </div>
    </div>
</div>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="form-container">
                <form method="post" enctype="multipart/form-data" novalidate id="productForm">
                    {% csrf_token %}

                    <!-- Basic Information Section -->
                    <div class="form-section">
                        <div class="section-title">
                            <div class="section-icon" style="background: var(--primary-gradient);">
                                <i class="bi bi-info-circle"></i>
                            </div>
                            المعلومات الأساسية
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.code.id_for_label }}" class="form-label required">
                                        <i class="bi bi-hash"></i>
                                        كود الصنف
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-hash"></i>
                                        </span>
                                        {{ form.code }}
                                    </div>
                                    {% if form.code.errors %}
                                        <div class="invalid-feedback">{{ form.code.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">كود فريد لتحديد الصنف (مثل: PRD001, ITM-2024-001)</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.barcode.id_for_label }}" class="form-label">
                                        <i class="bi bi-upc-scan"></i>
                                        الباركود
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-upc-scan"></i>
                                        </span>
                                        {{ form.barcode }}
                                    </div>
                                    {% if form.barcode.errors %}
                                        <div class="invalid-feedback">{{ form.barcode.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">رمز الباركود للمسح الضوئي (اختياري)</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="{{ form.name.id_for_label }}" class="form-label required">
                                        <i class="bi bi-tag"></i>
                                        اسم الصنف
                                    </label>
                                    {{ form.name }}
                                    {% if form.name.errors %}
                                        <div class="invalid-feedback">{{ form.name.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">اسم واضح ووصفي للصنف</div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.category.id_for_label }}" class="form-label">
                                        <i class="bi bi-tags"></i>
                                        الفئة
                                    </label>
                                    {{ form.category }}
                                    {% if form.category.errors %}
                                        <div class="invalid-feedback">{{ form.category.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">تصنيف الصنف</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.product_type.id_for_label }}" class="form-label required">
                                        <i class="bi bi-box"></i>
                                        نوع الصنف
                                    </label>
                                    {{ form.product_type }}
                                    {% if form.product_type.errors %}
                                        <div class="invalid-feedback">{{ form.product_type.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">نوع الصنف (منتج، خدمة، مادة خام، إلخ)</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.description.id_for_label }}" class="form-label">
                                        <i class="bi bi-file-text"></i>
                                        الوصف التفصيلي
                                    </label>
                                    {{ form.description }}
                                    {% if form.description.errors %}
                                        <div class="invalid-feedback">{{ form.description.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">وصف شامل للصنف ومواصفاته</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Pricing Section -->
                    <div class="form-section">
                        <div class="section-title">
                            <div class="section-icon" style="background: var(--success-gradient);">
                                <i class="bi bi-currency-dollar"></i>
                            </div>
                            معلومات التسعير
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.cost_price.id_for_label }}" class="form-label">
                                        <i class="bi bi-receipt"></i>
                                        سعر التكلفة
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">ريال</span>
                                        {{ form.cost_price }}
                                    </div>
                                    {% if form.cost_price.errors %}
                                        <div class="invalid-feedback">{{ form.cost_price.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">تكلفة شراء أو إنتاج الصنف</div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.selling_price.id_for_label }}" class="form-label">
                                        <i class="bi bi-tag"></i>
                                        سعر البيع
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">ريال</span>
                                        {{ form.selling_price }}
                                    </div>
                                    {% if form.selling_price.errors %}
                                        <div class="invalid-feedback">{{ form.selling_price.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">سعر بيع الصنف للعملاء</div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.wholesale_price.id_for_label }}" class="form-label">
                                        <i class="bi bi-boxes"></i>
                                        سعر الجملة
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">ريال</span>
                                        {{ form.wholesale_price }}
                                    </div>
                                    {% if form.wholesale_price.errors %}
                                        <div class="invalid-feedback">{{ form.wholesale_price.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">سعر البيع بالجملة (اختياري)</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Inventory Section -->
                    <div class="form-section">
                        <div class="section-title">
                            <div class="section-icon" style="background: var(--warning-gradient);">
                                <i class="bi bi-boxes"></i>
                            </div>
                            إدارة المخزون
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.main_unit.id_for_label }}" class="form-label">
                                        <i class="bi bi-rulers"></i>
                                        وحدة القياس
                                    </label>
                                    {{ form.main_unit }}
                                    {% if form.main_unit.errors %}
                                        <div class="invalid-feedback">{{ form.main_unit.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">وحدة قياس الصنف</div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.minimum_stock.id_for_label }}" class="form-label">
                                        <i class="bi bi-exclamation-triangle"></i>
                                        الحد الأدنى للمخزون
                                    </label>
                                    {{ form.minimum_stock }}
                                    {% if form.minimum_stock.errors %}
                                        <div class="invalid-feedback">{{ form.minimum_stock.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">تنبيه عند الوصول لهذا الحد</div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.maximum_stock.id_for_label }}" class="form-label">
                                        <i class="bi bi-box-fill"></i>
                                        الحد الأقصى للمخزون
                                    </label>
                                    {{ form.maximum_stock }}
                                    {% if form.maximum_stock.errors %}
                                        <div class="invalid-feedback">{{ form.maximum_stock.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">الحد الأقصى للتخزين</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Settings Section -->
                    <div class="form-section">
                        <div class="section-title">
                            <div class="section-icon" style="background: var(--info-gradient);">
                                <i class="bi bi-gear"></i>
                            </div>
                            الإعدادات
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3">
                                    {{ form.is_active }}
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        <i class="bi bi-toggle-on text-success"></i>
                                        الصنف نشط ومتاح للبيع
                                    </label>
                                    {% if form.is_active.errors %}
                                        <div class="invalid-feedback">{{ form.is_active.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3">
                                    {{ form.track_inventory }}
                                    <label class="form-check-label" for="{{ form.track_inventory.id_for_label }}">
                                        <i class="bi bi-graph-up text-info"></i>
                                        تتبع المخزون لهذا الصنف
                                    </label>
                                    {% if form.track_inventory.errors %}
                                        <div class="invalid-feedback">{{ form.track_inventory.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="form-actions">
                        <div class="d-flex justify-content-center gap-3">
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <span class="btn-text">
                                    <i class="bi bi-check-circle me-2"></i>
                                    {% if object %}تحديث الصنف{% else %}إضافة الصنف{% endif %}
                                </span>
                                <span class="btn-loading d-none">
                                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                    جاري المعالجة...
                                </span>
                            </button>
                            <a href="{% url 'definitions:product_list' %}" class="btn btn-secondary">
                                <i class="bi bi-x-circle me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('productForm');
    const submitBtn = document.getElementById('submitBtn');
    const btnText = submitBtn.querySelector('.btn-text');
    const btnLoading = submitBtn.querySelector('.btn-loading');

    form.addEventListener('submit', function(e) {
        // إظهار رسالة التحميل
        btnText.classList.add('d-none');
        btnLoading.classList.remove('d-none');
        submitBtn.disabled = true;

        // التحقق من صحة النموذج
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                isValid = false;
                field.classList.add('is-invalid');
            } else {
                field.classList.remove('is-invalid');
            }
        });

        if (!isValid) {
            e.preventDefault();
            // إخفاء رسالة التحميل
            btnText.classList.remove('d-none');
            btnLoading.classList.add('d-none');
            submitBtn.disabled = false;

            alert('يرجى ملء جميع الحقول المطلوبة');
            return false;
        }

        // السماح بإرسال النموذج
        console.log('إرسال النموذج...');
    });

    // إزالة رسالة التحميل في حالة حدوث خطأ
    window.addEventListener('beforeunload', function() {
        btnText.classList.remove('d-none');
        btnLoading.classList.add('d-none');
        submitBtn.disabled = false;
    });
});
</script>
{% endblock %}
