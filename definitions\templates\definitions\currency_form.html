{% extends 'base_form.html' %}

{% block title %}
{% if action == 'edit' %}
تعديل العملة - {{ currency.name }}
{% else %}
إضافة عملة جديدة
{% endif %}
{% endblock %}

{% block form_icon %}
<i class="bi bi-currency-exchange"></i>
{% endblock %}

{% block form_title %}
{% if action == 'edit' %}
تعديل العملة
{% else %}
إضافة عملة جديدة
{% endif %}
{% endblock %}

{% block back_url %}{% url 'definitions:currency_list' %}{% endblock %}

{% block submit_text %}
{% if action == 'edit' %}
حفظ التعديلات
{% else %}
إضافة العملة
{% endif %}
{% endblock %}

{% block form_content %}
<!-- Basic Information Section -->
<div class="form-section">
    <h3 class="section-title">
        <i class="bi bi-info-circle"></i>
        المعلومات الأساسية
    </h3>
    
    <div class="row">
        <div class="col-md-4 mb-3">
            <label for="code" class="form-label required">
                <i class="bi bi-hash"></i>كود العملة
            </label>
            <input type="text" 
                   class="form-control" 
                   id="code" 
                   name="code" 
                   value="{% if action == 'edit' %}{{ currency.code }}{% else %}{{ form_data.code|default:'' }}{% endif %}"
                   placeholder="مثال: USD، EUR، EGP"
                   maxlength="3"
                   style="text-transform: uppercase;"
                   required>
            <div class="form-text">كود العملة (3 أحرف)</div>
        </div>

        <div class="col-md-4 mb-3">
            <label for="name" class="form-label required">
                <i class="bi bi-translate"></i>اسم العملة
            </label>
            <input type="text" 
                   class="form-control" 
                   id="name" 
                   name="name" 
                   value="{% if action == 'edit' %}{{ currency.name }}{% else %}{{ form_data.name|default:'' }}{% endif %}"
                   placeholder="الاسم بالعربية"
                   required>
            <div class="form-text">اسم العملة بالعربية</div>
        </div>

        <div class="col-md-4 mb-3">
            <label for="name_en" class="form-label">
                <i class="bi bi-globe"></i>الاسم بالإنجليزية
            </label>
            <input type="text" 
                   class="form-control" 
                   id="name_en" 
                   name="name_en" 
                   value="{% if action == 'edit' %}{{ currency.name_en }}{% else %}{{ form_data.name_en|default:'' }}{% endif %}"
                   placeholder="Name in English">
            <div class="form-text">اسم العملة بالإنجليزية (اختياري)</div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6 mb-3">
            <label for="symbol" class="form-label required">
                <i class="bi bi-currency-dollar"></i>رمز العملة
            </label>
            <input type="text" 
                   class="form-control" 
                   id="symbol" 
                   name="symbol" 
                   value="{% if action == 'edit' %}{{ currency.symbol }}{% else %}{{ form_data.symbol|default:'' }}{% endif %}"
                   placeholder="مثال: $، €، ج.م"
                   maxlength="5"
                   required>
            <div class="form-text">رمز العملة للعرض</div>
        </div>

        <div class="col-md-6 mb-3">
            <label for="exchange_rate" class="form-label required">
                <i class="bi bi-arrow-left-right"></i>سعر الصرف
            </label>
            <input type="number" 
                   class="form-control" 
                   id="exchange_rate" 
                   name="exchange_rate" 
                   step="0.0001"
                   min="0"
                   value="{% if action == 'edit' %}{{ currency.exchange_rate }}{% else %}{{ form_data.exchange_rate|default:'1.0000' }}{% endif %}"
                   placeholder="1.0000"
                   required>
            <div class="form-text">سعر الصرف مقابل العملة الأساسية</div>
        </div>
    </div>
</div>

<!-- Additional Settings Section -->
<div class="form-section">
    <h3 class="section-title">
        <i class="bi bi-gear"></i>
        إعدادات إضافية
    </h3>
    
    <div class="row">
        <div class="col-md-6 mb-3">
            <div class="form-check">
                <input class="form-check-input" 
                       type="checkbox" 
                       id="is_base_currency" 
                       name="is_base_currency"
                       {% if action == 'edit' and currency.is_base_currency %}checked
                       {% elif form_data.is_base_currency %}checked{% endif %}>
                <label class="form-check-label" for="is_base_currency">
                    <i class="bi bi-star me-1"></i>عملة أساسية
                </label>
                <div class="form-text">تحديد هذه العملة كعملة أساسية للنظام</div>
            </div>
        </div>

        <div class="col-md-6 mb-3">
            <div class="form-check">
                <input class="form-check-input" 
                       type="checkbox" 
                       id="is_active" 
                       name="is_active"
                       {% if action == 'edit' and currency.is_active %}checked
                       {% elif action != 'edit' %}checked{% endif %}>
                <label class="form-check-label" for="is_active">
                    <i class="bi bi-toggle-on me-1"></i>عملة نشطة
                </label>
                <div class="form-text">تفعيل أو إلغاء تفعيل العملة</div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12 mb-3">
            <label for="notes" class="form-label">
                <i class="bi bi-journal-text"></i>ملاحظات
            </label>
            <textarea class="form-control" 
                      id="notes" 
                      name="notes" 
                      rows="3"
                      placeholder="ملاحظات إضافية عن العملة...">{% if action == 'edit' %}{{ currency.notes }}{% else %}{{ form_data.notes|default:'' }}{% endif %}</textarea>
            <div class="form-text">ملاحظات إضافية (اختياري)</div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحويل كود العملة إلى أحرف كبيرة تلقائياً
    const codeInput = document.getElementById('code');
    if (codeInput) {
        codeInput.addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });
    }
    
    // التحقق من العملة الأساسية
    const isBaseCurrencyCheckbox = document.getElementById('is_base_currency');
    const exchangeRateInput = document.getElementById('exchange_rate');
    
    if (isBaseCurrencyCheckbox && exchangeRateInput) {
        isBaseCurrencyCheckbox.addEventListener('change', function() {
            if (this.checked) {
                exchangeRateInput.value = '1.0000';
                exchangeRateInput.readOnly = true;
                exchangeRateInput.style.backgroundColor = '#f8f9fa';
            } else {
                exchangeRateInput.readOnly = false;
                exchangeRateInput.style.backgroundColor = '';
            }
        });
        
        // تطبيق الحالة الأولية
        if (isBaseCurrencyCheckbox.checked) {
            exchangeRateInput.value = '1.0000';
            exchangeRateInput.readOnly = true;
            exchangeRateInput.style.backgroundColor = '#f8f9fa';
        }
    }
});
</script>
{% endblock %}
