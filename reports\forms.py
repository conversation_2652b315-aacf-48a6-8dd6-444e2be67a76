from django import forms
from django.contrib.auth.models import User
from .models import ReportCategory, Report, SavedReport, ReportSchedule

class ReportCategoryForm(forms.ModelForm):
    """نموذج إضافة/تعديل فئات التقارير"""
    class Meta:
        model = ReportCategory
        fields = ['name', 'category_type', 'description', 'icon', 'is_active', 'order']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم الفئة'}),
            'category_type': forms.Select(attrs={'class': 'form-select'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'وصف الفئة'}),
            'icon': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'أيقونة Bootstrap'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'order': forms.NumberInput(attrs={'class': 'form-control', 'min': '0'}),
        }

class ReportForm(forms.ModelForm):
    """نموذج إضافة/تعديل التقارير"""
    class Meta:
        model = Report
        fields = ['category', 'name', 'description', 'report_type', 'query', 'template_name', 'is_public', 'status']
        widgets = {
            'category': forms.Select(attrs={'class': 'form-select'}),
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم التقرير'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'وصف التقرير'}),
            'report_type': forms.Select(attrs={'class': 'form-select'}),
            'query': forms.Textarea(attrs={'class': 'form-control', 'rows': 10, 'placeholder': 'استعلام SQL'}),
            'template_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم القالب (اختياري)'}),
            'is_public': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['category'].queryset = ReportCategory.objects.filter(is_active=True)

class SavedReportForm(forms.ModelForm):
    """نموذج حفظ التقارير"""
    class Meta:
        model = SavedReport
        fields = ['name', 'is_favorite', 'notes']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم التقرير المحفوظ'}),
            'is_favorite': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات'}),
        }

class ReportScheduleForm(forms.ModelForm):
    """نموذج جدولة التقارير"""
    class Meta:
        model = ReportSchedule
        fields = ['name', 'frequency', 'next_run', 'status']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم الجدولة'}),
            'frequency': forms.Select(attrs={'class': 'form-select'}),
            'next_run': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
        }

class ReportParametersForm(forms.Form):
    """نموذج معاملات التقرير"""
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
        label="من تاريخ"
    )
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
        label="إلى تاريخ"
    )
    branch = forms.ChoiceField(
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'}),
        label="الفرع"
    )
    department = forms.ChoiceField(
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'}),
        label="القسم"
    )
    employee = forms.ChoiceField(
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'}),
        label="الموظف"
    )
    status = forms.ChoiceField(
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'}),
        label="الحالة"
    )

    def __init__(self, *args, **kwargs):
        # استقبال الخيارات الديناميكية
        branch_choices = kwargs.pop('branch_choices', [])
        department_choices = kwargs.pop('department_choices', [])
        employee_choices = kwargs.pop('employee_choices', [])
        status_choices = kwargs.pop('status_choices', [])
        
        super().__init__(*args, **kwargs)
        
        # تحديث الخيارات
        if branch_choices:
            self.fields['branch'].choices = [('', 'جميع الفروع')] + branch_choices
        if department_choices:
            self.fields['department'].choices = [('', 'جميع الأقسام')] + department_choices
        if employee_choices:
            self.fields['employee'].choices = [('', 'جميع الموظفين')] + employee_choices
        if status_choices:
            self.fields['status'].choices = [('', 'جميع الحالات')] + status_choices

class ReportSearchForm(forms.Form):
    """نموذج البحث في التقارير"""
    search = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'البحث بالاسم أو الوصف...'
        })
    )
    category = forms.ModelChoiceField(
        queryset=ReportCategory.objects.filter(is_active=True),
        required=False,
        empty_label="جميع الفئات",
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    report_type = forms.ChoiceField(
        choices=[('', 'جميع الأنواع')] + Report.REPORT_TYPES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    status = forms.ChoiceField(
        choices=[('', 'جميع الحالات')] + Report.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    is_public = forms.ChoiceField(
        choices=[('', 'الكل'), ('True', 'عام'), ('False', 'خاص')],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

class ReportExecutionSearchForm(forms.Form):
    """نموذج البحث في تنفيذات التقارير"""
    report = forms.ModelChoiceField(
        queryset=Report.objects.filter(status='active'),
        required=False,
        empty_label="جميع التقارير",
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    executed_by = forms.ModelChoiceField(
        queryset=User.objects.filter(is_active=True),
        required=False,
        empty_label="جميع المستخدمين",
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    status = forms.ChoiceField(
        choices=[('', 'جميع الحالات')] + [
            ('pending', 'معلق'),
            ('running', 'قيد التنفيذ'),
            ('completed', 'مكتمل'),
            ('failed', 'فشل'),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
