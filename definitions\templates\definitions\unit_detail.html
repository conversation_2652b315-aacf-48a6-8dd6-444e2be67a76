{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل وحدة القياس - {{ unit.name }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: #ffffff;
        min-height: 100vh;
    }

    .detail-container {
        background: #ffffff;
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        color: #333;
    }

    .detail-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #e0e0e0;
    }

    .detail-title {
        font-size: 2rem;
        font-weight: 800;
        color: #333;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .unit-type-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        margin-left: 1rem;
    }

    .unit-type-badge.weight { background: #e3f2fd; color: #1976d2; border: 1px solid #bbdefb; }
    .unit-type-badge.length { background: #e8f5e8; color: #388e3c; border: 1px solid #c8e6c9; }
    .unit-type-badge.area { background: #fff3e0; color: #f57c00; border: 1px solid #ffcc02; }
    .unit-type-badge.volume { background: #f3e5f5; color: #7b1fa2; border: 1px solid #e1bee7; }
    .unit-type-badge.quantity { background: #fce4ec; color: #c2185b; border: 1px solid #f8bbd9; }
    .unit-type-badge.time { background: #e0f2f1; color: #00695c; border: 1px solid #b2dfdb; }
    .unit-type-badge.other { background: #f5f5f5; color: #616161; border: 1px solid #e0e0e0; }

    .action-buttons {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .btn {
        padding: 0.6rem 1.5rem;
        border-radius: 6px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        font-size: 0.875rem;
    }

    .btn-primary {
        background: #007bff;
        color: white;
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
    }

    .btn-primary:hover {
        background: #0056b3;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-outline-secondary {
        background: transparent;
        border: 2px solid #6c757d;
        color: #6c757d;
    }

    .btn-outline-secondary:hover {
        background: #6c757d;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    .btn-danger {
        background: #dc3545;
        color: white;
        box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
    }

    .btn-danger:hover {
        background: #c82333;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
        color: white;
        text-decoration: none;
    }

    .detail-section {
        background: #f8f9fa;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .section-title {
        font-size: 1.2rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .detail-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
    }

    .detail-item {
        display: flex;
        flex-direction: column;
    }

    .detail-label {
        font-weight: 600;
        color: #666;
        font-size: 0.875rem;
        margin-bottom: 0.25rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .detail-value {
        color: #333;
        font-size: 1rem;
        padding: 0.5rem 0;
        border-bottom: 1px solid #e0e0e0;
    }

    .detail-value.empty {
        color: #999;
        font-style: italic;
    }

    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.3rem 0.6rem;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .status-badge.active {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .status-badge.inactive {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .status-badge.base-unit {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }

    @media (max-width: 768px) {
        .detail-container {
            padding: 1.5rem;
        }
        
        .detail-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }
        
        .detail-title {
            font-size: 1.5rem;
        }
        
        .action-buttons {
            width: 100%;
            justify-content: stretch;
        }
        
        .btn {
            flex: 1;
            justify-content: center;
        }
        
        .detail-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="detail-container">
                <div class="detail-header">
                    <div>
                        <h1 class="detail-title">
                            <i class="bi bi-rulers"></i>
                            {{ unit.name }}
                            <span class="unit-type-badge {{ unit.unit_type }}">
                                {{ unit.get_unit_type_display }}
                            </span>
                            {% if unit.is_base_unit %}
                                <span class="status-badge base-unit">
                                    <i class="bi bi-star me-1"></i>وحدة أساسية
                                </span>
                            {% endif %}
                        </h1>
                        <p class="text-muted mb-0">
                            <i class="bi bi-hash me-1"></i>
                            كود: {{ unit.code }}
                        </p>
                    </div>
                    
                    <div class="action-buttons">
                        <a href="{% url 'definitions:unit_list' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-right me-2"></i>عودة للقائمة
                        </a>
                        <a href="{% url 'definitions:unit_edit' unit.id %}" class="btn btn-primary">
                            <i class="bi bi-pencil me-2"></i>تعديل
                        </a>
                        <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                            <i class="bi bi-trash me-2"></i>حذف
                        </button>
                    </div>
                </div>

                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            <i class="bi bi-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% else %}info-circle{% endif %} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <!-- Basic Information Section -->
                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="bi bi-info-circle"></i>
                        المعلومات الأساسية
                    </h3>
                    
                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-hash"></i>كود وحدة القياس
                            </div>
                            <div class="detail-value">{{ unit.code }}</div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-rulers"></i>الاسم
                            </div>
                            <div class="detail-value">{{ unit.name }}</div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-translate"></i>الاسم بالإنجليزية
                            </div>
                            <div class="detail-value {% if not unit.name_en %}empty{% endif %}">
                                {{ unit.name_en|default:"غير محدد" }}
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-tags"></i>نوع وحدة القياس
                            </div>
                            <div class="detail-value">
                                <span class="unit-type-badge {{ unit.unit_type }}">
                                    {{ unit.get_unit_type_display }}
                                </span>
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-123"></i>عدد الخانات العشرية
                            </div>
                            <div class="detail-value">{{ unit.decimal_places }}</div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-toggle-on"></i>الحالة
                            </div>
                            <div class="detail-value">
                                <span class="status-badge {% if unit.is_active %}active{% else %}inactive{% endif %}">
                                    {% if unit.is_active %}
                                        <i class="bi bi-check-circle me-1"></i>نشط
                                    {% else %}
                                        <i class="bi bi-x-circle me-1"></i>غير نشط
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Information Section -->
                {% if unit.description or unit.notes %}
                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="bi bi-file-text"></i>
                        معلومات إضافية
                    </h3>
                    
                    <div class="detail-grid">
                        {% if unit.description %}
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-file-text"></i>الوصف
                            </div>
                            <div class="detail-value">{{ unit.description|linebreaks }}</div>
                        </div>
                        {% endif %}

                        {% if unit.notes %}
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-journal-text"></i>ملاحظات
                            </div>
                            <div class="detail-value">{{ unit.notes|linebreaks }}</div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- System Information Section -->
                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="bi bi-gear"></i>
                        معلومات النظام
                    </h3>
                    
                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-person-plus"></i>أنشأ بواسطة
                            </div>
                            <div class="detail-value">
                                {{ unit.created_by.get_full_name|default:unit.created_by.username }}
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-calendar-plus"></i>تاريخ الإنشاء
                            </div>
                            <div class="detail-value">
                                {{ unit.created_at|date:"d/m/Y H:i" }}
                            </div>
                        </div>

                        {% if unit.updated_at %}
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-calendar-check"></i>آخر تحديث
                            </div>
                            <div class="detail-value">
                                {{ unit.updated_at|date:"d/m/Y H:i" }}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="bi bi-exclamation-triangle text-danger me-2"></i>
                    تأكيد الحذف
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف وحدة القياس <strong>"{{ unit.name }}"</strong>؟</p>
                <p class="text-danger">
                    <i class="bi bi-exclamation-triangle me-1"></i>
                    هذا الإجراء لا يمكن التراجع عنه!
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x me-2"></i>إلغاء
                </button>
                <form method="post" action="{% url 'definitions:unit_delete' unit.id %}" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash me-2"></i>حذف نهائي
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete() {
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
{% endblock %}
